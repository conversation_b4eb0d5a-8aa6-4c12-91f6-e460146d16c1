import os
import time
import random
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import requests
from twilio.rest import Client
import threading
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import gc  # For garbage collection
from typing import Optional, Iterator
import shutil  # For directory operations
import psutil  # For memory monitoring
import sys  # For platform detection

# Twilio credentials
account_sid = '**********************************'
auth_token = '884f6841156a0309ce9e49358101c0e6'
flow_sid = "FWe80b98a9b5f3667cc5d1e26e02d8d4ec"     
to_phone_number = "+************"
from_twilio_number = "+***********"

# Global variables
driver = None
csv_iterator: Optional[Iterator] = None
current_chunk = None
current_row = 0
total_rows = 0
chunk_size = 100  # Reduced chunk size for better memory efficiency
automation_active = False
paused = False
aspect_ratios = ["16:9", "4:3", "1:1", "21:9", "3:2"]

# Stock image aspect ratios based on common orientations
stock_portrait_ratios = ["3:2", "4:3", "1:1"]  # Portrait and square formats
stock_landscape_ratios = ["16:9", "21:9", "3:2"]  # Landscape formats
stock_square_ratios = ["1:1"]  # Square format

# Keywords to detect stock image prompts
stock_keywords = [
    "stock photo", "stock photography", "stock image", "commercial stock",
    "studio portrait", "professional portrait", "studio photography",
    "commercial photography", "lifestyle photography", "product photography",
    "beauty shot", "headshot", "corporate portrait", "business portrait"
]

def detect_stock_image_type(prompt_text):
    """
    Detect if prompt is for stock image and determine appropriate aspect ratio category
    Returns: 'portrait', 'landscape', 'square', or None
    """
    prompt_lower = prompt_text.lower()
    
    # Check if it's a stock image prompt
    is_stock = any(keyword in prompt_lower for keyword in stock_keywords)
    
    if not is_stock:
        return None
    
    # Determine orientation based on content
    portrait_indicators = [
        "portrait", "headshot", "person", "face", "model", "woman", "man",
        "businesswoman", "businessman", "professional", "therapist", "client",
        "individual", "subject", "human", "people", "family", "child", "adult"
    ]
    
    landscape_indicators = [
        "scene", "landscape", "cityscape", "market", "environment", "interior",
        "room", "space", "background", "setting", "location", "venue",
        "panoramic", "wide", "horizontal", "nightlife", "urban", "balcony"
    ]
    
    square_indicators = [
        "template", "diagram", "interface", "logo", "icon", "graphic",
        "design element", "minimalist", "centered", "symmetric"
    ]
    
    # Count indicators
    portrait_count = sum(1 for indicator in portrait_indicators if indicator in prompt_lower)
    landscape_count = sum(1 for indicator in landscape_indicators if indicator in prompt_lower)
    square_count = sum(1 for indicator in square_indicators if indicator in prompt_lower)
    
    # Determine type based on highest count
    if square_count > 0 and square_count >= max(portrait_count, landscape_count):
        return 'square'
    elif landscape_count > portrait_count:
        return 'landscape'
    else:
        return 'portrait'  # Default to portrait for people-focused stock images

def get_stock_aspect_ratio(prompt_text):
    """
    Get appropriate aspect ratio for stock image based on content analysis
    """
    stock_type = detect_stock_image_type(prompt_text)
    
    if stock_type == 'portrait':
        return random.choice(stock_portrait_ratios)
    elif stock_type == 'landscape':
        return random.choice(stock_landscape_ratios)
    elif stock_type == 'square':
        return random.choice(stock_square_ratios)
    else:
        # Not a stock image, use regular randomization
        return random.choice(aspect_ratios)
automation_timeout = 5000  # Default timeout
rows_to_process = 0
call_count = 0  # Counter for calls during error
loaded_csv_path = None  # Store the loaded csv path
last_cleanup_time = time.time()  # Track last time cleanup was performed
rate_limit_count = 0  # Count of prompts sent within the current minute
rate_limit_start_time = time.time()  # Time when rate counting started
rate_limit_per_minute = 3  # Number of prompts to send per minute
log_buffer = []  # Buffer for logs to reduce UI updates
log_last_update = time.time()  # Track when log was last updated
browser_last_used = time.time()  # Track when browser was last used
browser_idle_timeout = 300  # Close browser after 5 minutes (300 seconds) of inactivity
# Log levels: 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
log_level = 2  # Default to WARNING level
rate_limit_queue = []  # Queue to track submission timestamps
rate_limit_lock = threading.Lock()  # Lock for thread-safe rate limiting

# New variables for prompt tracking
prompts_sent_today = 0
prompts_sent_total = 0
prompt_history = []  # Store recent prompts with timestamps
last_prompt_time = None
prompt_tracking_file = "prompt_history.csv"  # File to store prompt history
# Tkinter root window
root = tk.Tk()
root.title("CSV Submitter Tool v2")
root.geometry("600x500")  # Smaller window
root.resizable(True, True)

# Initialize Tkinter variables after root
delete_after_sent = tk.BooleanVar(value=True)
use_ar_feature = tk.BooleanVar(value=True)
selected_encoding = tk.StringVar(value="utf-8")
log_level_var = tk.StringVar(value="Warning")  # Default log level

class CSVHandler:
    @staticmethod
    def get_chunk_iterator(file_path: str, encoding: str, chunk_size: int = 100) -> tuple[Iterator, int]:
        """Returns a chunk iterator and total number of rows for the CSV file."""
        # Count total rows more efficiently
        with open(file_path, 'r', encoding=encoding) as f:
            total_rows = sum(1 for _ in f) - 1  # subtract header
        
        # Create the chunk iterator with memory optimizations
        iterator = pd.read_csv(
            file_path,
            encoding=encoding,
            chunksize=chunk_size,
            low_memory=True,
            engine='c',  # Use faster C engine
            usecols=['input_data'],  # Only load required column
            dtype={'input_data': 'string'},  # Use string dtype for memory efficiency
            na_filter=False  # Disable NA detection for speed
        )
        
        return iterator, total_rows

    @staticmethod
    def save_progress(file_path: str, processed_rows: list, encoding: str, max_retries: int = 3):
        """Save progress by removing processed rows from the CSV with retry mechanism."""
        for retry in range(max_retries):
            try:
                if not os.path.exists(file_path):
                    log(f"Error: CSV file not found at {file_path}", 2)
                    return False
                    
                # Create backup only once per session or hour to reduce disk I/O
                backup_frequency = 3600  # 1 hour in seconds
                backup_path = f"{file_path}.bak"
                current_time = time.time()
                
                # Only make a backup if the last one is older than backup_frequency
                if not hasattr(CSVHandler, 'last_backup_time') or current_time - CSVHandler.last_backup_time > backup_frequency:
                    try:
                        shutil.copy2(file_path, backup_path)
                        CSVHandler.last_backup_time = current_time
                    except Exception as e:
                        log(f"Warning: Could not create backup file: {e}", 2)
                
                # Load and process CSV in smaller chunks to reduce memory usage
                chunk_size = 50  # Smaller chunks for memory efficiency
                temp_file = f"{file_path}.temp"
                header_written = False
                
                # Process chunks one by one without storing in memory
                for chunk in pd.read_csv(file_path, encoding=encoding, chunksize=chunk_size, low_memory=True):
                    # Remove processed rows from chunk
                    chunk = chunk[~chunk.index.isin(processed_rows)]
                    
                    if not chunk.empty:
                        # Write chunk to temp file
                        chunk.to_csv(temp_file, mode='a', header=not header_written, index=False, encoding=encoding)
                        header_written = True
                    
                    # Force garbage collection after each chunk
                    del chunk
                    gc.collect()
                
                # Verify temp file exists and has content
                if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:

                        try:
                            # Verify file can be read
                            pd.read_csv(temp_file, encoding=encoding, nrows=1)
                            # Replace original file
                            os.replace(temp_file, file_path)
                            log(f"Successfully deleted {len(processed_rows)} row(s) from CSV", 1)
                            return True
                        except Exception as e:
                            log(f"Error: Temp CSV file is invalid: {e}", 3)
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                            if retry < max_retries - 1:
                                log(f"Retrying deletion (attempt {retry + 2}/{max_retries})...", 1)
                                time.sleep(1)  # Wait before retry
                                continue
                            return False
                else:
                        log(f"Error: Temp CSV file is missing or empty", 3)
                        return False
                        
                return True
                
            except Exception as e:
                log(f"Error saving progress (attempt {retry + 1}/{max_retries}): {e}", 3)
                if retry < max_retries - 1:
                    time.sleep(1)  # Wait before retry
                    continue
                
                # Try to restore from backup if available
                backup_path = f"{file_path}.bak"
                if os.path.exists(backup_path):
                    try:
                        shutil.copy2(backup_path, file_path)
                        log("Restored CSV file from backup after error", 2)
                    except Exception as restore_error:
                        log(f"Failed to restore CSV from backup: {restore_error}", 3)
                return False
        
        return False

def log(message, level=1):
    """
    Logs a message to the status log and the console.
    Levels: 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
    """
    global log_buffer, log_last_update
    
    if level < log_level:
        return
        
    level_prefixes = {0: "[DEBUG] ", 1: "", 2: "[WARNING] ", 3: "[ERROR] "}
    prefixed_message = f"{level_prefixes[level]}{message}"
    
    print(prefixed_message)
    
    if level >= 2:  # WARNING and ERROR - immediate update
        if threading.current_thread() is threading.main_thread():
            status_log.insert(tk.END, prefixed_message + '\n')
            status_log.see(tk.END)
            if status_log.index(tk.END).split('.')[0] > '100':
                status_log.delete(1.0, "50.0")
        else:
            root.after(0, lambda: _immediate_log_update(prefixed_message))
    else:
        log_buffer.append(prefixed_message)
        current_time = time.time()
        
        if len(log_buffer) >= 5 or current_time - log_last_update > 1.0:
            if threading.current_thread() is threading.main_thread():
                _flush_log_buffer()
            else:
                root.after(0, _flush_log_buffer)

def _immediate_log_update(message):
    """Immediately update log for critical messages"""
    status_log.insert(tk.END, message + '\n')
    status_log.see(tk.END)
    if status_log.index(tk.END).split('.')[0] > '100':
        status_log.delete(1.0, "50.0")

def _flush_log_buffer():
    """Flush the log buffer to the UI."""
    global log_buffer, log_last_update
    
    if log_buffer:
        if len(log_buffer) > 10:
            log_buffer = log_buffer[-10:]
            
        messages = '\n'.join(log_buffer) + '\n'
        status_log.insert(tk.END, messages)
        
        current_lines = int(status_log.index(tk.END).split('.')[0])
        if current_lines > 50:  
            status_log.delete(1.0, "25.0")  
            
        status_log.see(tk.END)
        log_buffer = []
        log_last_update = time.time()

def set_log_level():
    """Update the log level from the dropdown."""
    global log_level
    selected = log_level_var.get()
    if selected == "Debug":
        log_level = 0
    elif selected == "Info":
        log_level = 1
    elif selected == "Warning":
        log_level = 2
    elif selected == "Error":
        log_level = 3
    log(f"Log level set to: {selected}", 1)

def load_csv():
    global csv_iterator, current_chunk, current_row, rows_to_process, loaded_csv_path, total_rows
    
    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        log("No CSV loaded")
        return

    try:
        encoding = selected_encoding.get()
        try:
            csv_iterator, total_rows = CSVHandler.get_chunk_iterator(file_path, encoding, chunk_size)
            current_chunk = next(csv_iterator)
            log(f"Loaded CSV with {encoding} encoding - Total rows: {total_rows}")
        except UnicodeDecodeError:
            # Try alternative encodings
            for alt_encoding in ['latin1', 'ISO-8859-1', 'cp1252']:
                if alt_encoding == encoding:
                    continue
                try:
                    csv_iterator, total_rows = CSVHandler.get_chunk_iterator(file_path, alt_encoding, chunk_size)
                    current_chunk = next(csv_iterator)
                    selected_encoding.set(alt_encoding)
                    log(f"Loaded CSV with {alt_encoding} encoding - Total rows: {total_rows}")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError("Could not decode file with any common encoding")

        current_row = 1  # Start row counting from 1 for better readability
        rows_to_process = total_rows
        row_count_entry.delete(0, tk.END)
        row_count_entry.insert(0, str(total_rows))
        loaded_csv_path = file_path
        
        # Enable buttons
        submit_button.config(state=tk.NORMAL)
        start_button.config(state=tk.NORMAL)
        row_count_entry.config(state=tk.NORMAL)
        
        # Force garbage collection
        gc.collect()
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to load CSV file: {e}")
        log(f"Failed to load CSV: {e}")
        start_button.config(state=tk.DISABLED)
        row_count_entry.config(state=tk.DISABLED)

def get_next_row_data():
    global current_chunk, csv_iterator, current_row
    
    try:
        # If we've processed all rows in the current chunk, get the next chunk
        if current_chunk is None or (current_row-1) >= len(current_chunk):
            try:
                # Clean up previous chunk from memory
                if current_chunk is not None:
                    del current_chunk
                    gc.collect()
                
                current_chunk = next(csv_iterator)
                current_row = 1  # Reset to 1 for the new chunk
                
                # If the chunk is empty, try to get the next chunk
                while len(current_chunk) == 0 and csv_iterator is not None:
                    try:
                        del current_chunk
                        current_chunk = next(csv_iterator)
                        gc.collect()
                    except StopIteration:
                        return None
            except StopIteration:
                return None

        # Get the data from the current chunk (adjust for 1-based counting)
        row_data = current_chunk.iloc[current_row-1]['input_data']
        current_row += 1
        
        return str(row_data).strip() if pd.notna(row_data) else None
        
    except Exception as e:
        log(f"Error getting next row: {e}")
        return None

def initialize_driver():
    global driver
    if driver is None:
        try:
            options = uc.ChromeOptions()
            
            # ULTRA-LIGHTWEIGHT: Minimal Chrome options for maximum efficiency
            options.add_argument('--headless')  # Run in headless mode
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-images')
            options.add_argument('--disable-javascript')  # Keep if Midjourney works without JS
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-logging')
            options.add_argument('--disable-gpu-logging')
            options.add_argument('--silent')
            options.add_argument('--no-crash-upload')
            options.add_argument('--memory-pressure-off')
            options.add_argument('--max_old_space_size=1024')  # Further reduced memory
            options.add_argument('--aggressive-cache-discard')
            options.add_argument('--single-process')  # Use single process mode
            options.add_argument('--disable-features=VizDisplayCompositor,AudioServiceOutOfProcess')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-component-update')
            options.add_argument('--disable-domain-reliability')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--no-first-run')
            options.add_argument('--disable-web-security')

            # Additional lightweight options
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--window-size=800,600')  # Smaller window size
            
            # REMOVED: Many redundant options that were causing overhead
            
            # Use a custom profile directory
            profile_path = os.path.join(os.getcwd(), 'chrome_profile')
            if not os.path.exists(profile_path):
                os.makedirs(profile_path)
            options.add_argument(f'--user-data-dir={profile_path}')
            
            try:
                driver = uc.Chrome(options=options, suppress_welcome=True)
                driver.set_page_load_timeout(30)  
                driver.set_script_timeout(30)    
                driver.set_window_size(1280, 720)  
                driver.delete_all_cookies()
                
                log("Web driver initialized successfully.")
                return driver
            except Exception as chrome_error:
                log(f"Chrome initialization error: {chrome_error}")
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
                driver = None
                raise
        except Exception as e:
            log(f"Failed to initialize driver: {e}")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            driver = None
            return None
    return driver

def make_call():
    global call_count
    try:
        if call_count < 2: 
            client = Client(account_sid, auth_token)
            execution = client.studio.v2.flows(flow_sid).executions.create(
                to=to_phone_number,
                from_=from_twilio_number
            )
            call_count += 1
            log(f"Flow execution started successfully. SID: {execution.sid}. Call count : {call_count}")
        else:
           log(f"Call limit exceeded. Current call count : {call_count}")
    except Exception as e:
        log(f"Failed to initiate call: {e}")

def check_rate_limit():
    """
    Enhanced rate limit checker with queue system.
    Returns True if we can proceed, False if we need to wait.
    """
    global rate_limit_queue, rate_limit_start_time, rate_limit_count
    
    with rate_limit_lock:  
        current_time = time.time()
        
        # Remove timestamps older than 1 minute from queue
        while rate_limit_queue and current_time - rate_limit_queue[0] >= 60:
            rate_limit_queue.pop(0)
        
        # Check if we're within rate limit
        if len(rate_limit_queue) < rate_limit_per_minute:
            rate_limit_queue.append(current_time)
            remaining = rate_limit_per_minute - len(rate_limit_queue)
            log(f"Rate limit: {len(rate_limit_queue)}/{rate_limit_per_minute} prompts this minute. {remaining} remaining.", 0)
            return True
        
        # Calculate wait time until next slot opens
        if rate_limit_queue:
            wait_time = 60 - (current_time - rate_limit_queue[0])
            if wait_time > 0:
                log(f"Rate limit reached. Next slot available in {wait_time:.1f} seconds.", 1)
            return False
    
    return True

def track_prompt(prompt_text):
    """Track a sent prompt and update statistics"""
    global prompts_sent_today, prompts_sent_total, last_prompt_time, prompt_history
    
    current_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # Update counters
    prompts_sent_today += 1
    prompts_sent_total += 1
    last_prompt_time = current_time
    
    # Truncate prompt text for display
    short_prompt = prompt_text[:50] + "..." if len(prompt_text) > 50 else prompt_text
    
    # Add to history (keep last 100 prompts)
    prompt_history.append({"time": timestamp, "prompt": short_prompt})
    if len(prompt_history) > 100:
        prompt_history = prompt_history[-100:]
    
    # Save to CSV file (append mode)
    try:
        with open(prompt_tracking_file, "a", encoding="utf-8") as f:
            if os.path.getsize(prompt_tracking_file) == 0:
                f.write("timestamp,prompt\n")
            f.write(f"\"{timestamp}\",\"{prompt_text.replace('"', '""')}\"\n")
    except Exception as e:
        log(f"Failed to save prompt history: {e}", 2)
    
    # Update the UI
    update_prompt_stats()

def update_prompt_stats():
    """Update the prompt statistics display"""
    if not hasattr(update_prompt_stats, "last_update") or time.time() - update_prompt_stats.last_update > 1.0:
        # Only update UI every second to reduce overhead
        try:
            # Update today count
            prompts_today_value.config(text=str(prompts_sent_today))
            
            # Update total count
            prompts_total_value.config(text=str(prompts_sent_total))
            
            # Update last sent time
            if last_prompt_time:
                time_diff = time.time() - last_prompt_time
                if time_diff < 60:
                    time_text = f"{int(time_diff)} seconds ago"
                elif time_diff < 3600:
                    time_text = f"{int(time_diff/60)} minutes ago"
                else:
                    time_text = f"{int(time_diff/3600)} hours ago"
                last_prompt_time_value.config(text=time_text)
            
            # Update rate display
            current_rate = len(rate_limit_queue)
            rate_display.config(text=f"{current_rate}/{rate_limit_per_minute} per minute")
            
            # Update progress bar
            if total_rows > 0 and current_row > 0:
                progress_percent = min(100, (current_row / total_rows) * 100)
                progress_bar["value"] = progress_percent
                progress_text.config(text=f"{progress_percent:.1f}% ({current_row}/{total_rows})")
            
            update_prompt_stats.last_update = time.time()
        except Exception as e:
            log(f"Error updating prompt stats: {e}", 0)

def load_prompt_history():
    """Load prompt history from file on startup"""
    global prompts_sent_total, prompt_history
    
    try:
        if os.path.exists(prompt_tracking_file):
            df = pd.read_csv(prompt_tracking_file, encoding="utf-8")
            prompts_sent_total = len(df)
            
            # Get today's count
            today = time.strftime("%Y-%m-%d")
            today_df = df[df['timestamp'].str.startswith(today)]
            global prompts_sent_today
            prompts_sent_today = len(today_df)
            
            # Load recent history
            recent = df.tail(100).to_dict('records')
            prompt_history = [{"time": r["timestamp"], "prompt": r["prompt"]} for r in recent]
            
            log(f"Loaded prompt history: {prompts_sent_total} total, {prompts_sent_today} today", 1)
    except Exception as e:
        log(f"Failed to load prompt history: {e}", 2)

def submit_next_row():
    global current_row, browser_last_used
    
    if csv_iterator is None or current_chunk is None:
        log("Please load a CSV file first")
        return
    
    browser_last_used = time.time()
    
    # OPTIMIZED: Simplified rate limiting
    if not check_rate_limit():
        log("Rate limit reached. Please wait.", 1)
        return
    
    try:
        input_value = get_next_row_data()
        if input_value is None:
            log(f"No more data at row {current_row}")
            return
        
        input_value = str(input_value).strip().replace('\n', ' ').replace('\r', ' ')
        if not input_value:
            log(f"Empty data at row {current_row}")
            return

        if use_ar_feature.get():
            # Use intelligent stock image ratio detection
            smart_aspect_ratio = get_stock_aspect_ratio(input_value)
            input_value_with_ar = f"{input_value} --ar {smart_aspect_ratio}"
        else:
            input_value_with_ar = input_value
        
        # OPTIMIZED: Reduced wait times
        textarea = WebDriverWait(driver, 8).until(  
            EC.presence_of_element_located((By.XPATH, "/html/body/div/div[1]/div/div[4]/div/div[1]/div/div[1]/div/textarea"))
        )
        
        textarea.clear()
        textarea.send_keys(input_value_with_ar)
        time.sleep(1)  
        textarea.send_keys(Keys.ENTER)
        
        # Track the prompt
        track_prompt(input_value_with_ar)
        
        # OPTIMIZED: Background CSV update without blocking UI
        if delete_after_sent.get():
            threading.Thread(
                target=lambda: CSVHandler.save_progress(
                    loaded_csv_path, 
                    [current_chunk.index[current_row - 2]], 
                    selected_encoding.get()
                ), 
                daemon=True
            ).start()
        
        log(f"Submitted row {current_row}", 1)
        
    except Exception as e:
        log(f"Submission failed at row {current_row}: {str(e)}", 3)
        threading.Thread(target=make_call, daemon=True).start()

def pause_automation():
    global paused
    paused = True
    log("Automation paused.")
    pause_button.config(state=tk.DISABLED)
    resume_button.config(state=tk.NORMAL)

def resume_automation():
    global paused
    paused = False
    log("Resuming automation...")
    resume_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    automate_submission()

def open_web_page():
    global driver
    try:
        driver = initialize_driver()
        if driver is None:
            messagebox.showerror("Error", "Failed to initialize Chrome driver")
            return
            
        driver.get("https://www.midjourney.com/imagine")
        log("Page loaded, ready to submit!")
        open_page_button.config(state=tk.DISABLED)  
    except Exception as e:
        log(f"Failed to open web page: {e}")
        messagebox.showerror("Error", f"Failed to open web page: {e}")
        if driver:
            try:
                driver.quit()
            except:
                pass
            driver = None

def start_automation():
    global automation_active, rows_to_process, current_row, call_count, driver
    
    try:
        rows_to_process = int(row_count_entry.get())
        if not isinstance(rows_to_process, int) or rows_to_process <= 0:
            messagebox.showerror("Error", "Please enter a valid positive integer for row count.")
            return
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for row count.")
        return
        
    if csv_iterator is None:
        log("Please load a CSV file first")
        return
        
    if not driver:
        initialize_driver()
        
    automation_active = True
    start_button.config(state=tk.DISABLED)  
    pause_button.config(state=tk.NORMAL)  
    log("Automation started...")
    status_log.delete(1.0, tk.END)  
    current_row = 1  
    call_count = 0  
    # Perform cleanup before starting
    cleanup_chrome_profile()
    
    # Start automation in a separate thread
    automation_thread = threading.Thread(target=automate_submission)
    automation_thread.daemon = True
    automation_thread.start()

def stop_automation():
    global automation_active
    automation_active = False
    start_button.config(state=tk.NORMAL)
    pause_button.config(state=tk.DISABLED)
    log("Automation stopped.")

def check_browser_health():
    """Checks if the browser is still responsive and restarts if needed."""
    global driver, browser_last_used
    
    try:
        # Check if browser should be closed due to inactivity
        if driver and time.time() - browser_last_used > browser_idle_timeout:
            log("Browser inactive for too long. Closing to save resources.", 1)
            try:
                driver.quit()
            except:
                pass
            driver = None
            return False
            
        if driver is None:
            log("Browser session not initialized. Initializing...")
            driver = initialize_driver()
            if driver:
                driver.get("https://www.midjourney.com/imagine")
                browser_last_used = time.time()  
                return True
            return False
            
        # Enhanced browser health checks
        try:
            # Try to execute a simple JavaScript command to check responsiveness
            driver.execute_script("return document.readyState")
            
            # Check if page is still accessible
            current_url = driver.current_url
            if "midjourney.com" not in current_url.lower():
                log("Browser navigated away from Midjourney. Returning to correct page...", 2)
                driver.get("https://www.midjourney.com/imagine")
                time.sleep(2)  
            
            # Check for common error states
            error_indicators = [
                "//div[contains(text(), 'Error')]",
                "//div[contains(text(), 'Something went wrong')]",
                "//div[contains(text(), 'Please try again')]"
            ]
            
            for indicator in error_indicators:
                if driver.find_elements(By.XPATH, indicator):
                    log("Error state detected in browser. Refreshing page...", 2)
                    driver.refresh()
                    time.sleep(3)  
                    break
            
            # Check memory usage and restart if too high
            try:
                process = psutil.Process(driver.service.process.pid)
                memory_usage = process.memory_info().rss / (1024 * 1024)  
                
                if memory_usage > 800:  # Reduced threshold for better memory management
                    log(f"Chrome is using {memory_usage:.1f}MB. Restarting browser...", 2)
                    raise Exception("Memory threshold exceeded")
            except:
                pass
                
            browser_last_used = time.time()  
            return True
            
        except Exception as check_error:
            log(f"Browser health check failed: {check_error}", 2)
            raise  # Re-raise to trigger browser restart
            
    except Exception as e:
        log(f"Browser needs restart: {e}")
        try:
            if driver:
                current_url = driver.current_url  
                driver.quit()
        except:
            pass
        driver = None
        
        # Wait a moment before restarting
        time.sleep(3)
        
        # Attempt to recover browser session
        log("Attempting to recover browser session...")
        max_retries = 3
        for retry in range(max_retries):
            try:
                driver = initialize_driver()
                if driver:
                    driver.get("https://www.midjourney.com/imagine")
                    browser_last_used = time.time()  
                    log("Browser session recovered successfully")
                    return True
            except Exception as retry_error:
                if retry < max_retries - 1:
                    log(f"Retry {retry + 1}/{max_retries} failed. Waiting before next attempt...", 2)
                    time.sleep(5)  
                else:
                    log("Failed to recover browser session after all retries", 3)
        return False

def cleanup_chrome_profile():
    """Cleans up unnecessary Chrome profile files to prevent bloat."""
    global last_cleanup_time
    
    try:
        profile_path = os.path.join(os.getcwd(), 'chrome_profile')
        
        # Directories that can be safely cleaned
        safe_to_clean = [
            os.path.join(profile_path, 'Default', 'Cache'),
            os.path.join(profile_path, 'Default', 'Code Cache'),
            os.path.join(profile_path, 'Default', 'GPUCache'),
            os.path.join(profile_path, 'Default', 'Service Worker', 'CacheStorage'),
            os.path.join(profile_path, 'Default', 'Service Worker', 'ScriptCache'),
            os.path.join(profile_path, 'Default', 'Application Cache'),
            os.path.join(profile_path, 'Default', 'File System'),
            os.path.join(profile_path, 'Default', 'blob_storage'),
            os.path.join(profile_path, 'Default', 'IndexedDB'),
            os.path.join(profile_path, 'Default', 'Session Storage'),
            os.path.join(profile_path, 'GrShaderCache'),
            os.path.join(profile_path, 'ShaderCache'),
            os.path.join(profile_path, 'optimization_guide_prediction_model_downloads'),
        ]
        
        # Calculate size before cleanup
        total_size_before = 0
        for directory in safe_to_clean:
            if os.path.exists(directory):
                for dirpath, _, filenames in os.walk(directory):
                    for f in filenames:
                        fp = os.path.join(dirpath, f)
                        if os.path.exists(fp):
                            total_size_before += os.path.getsize(fp)
        
        cleaned_count = 0
        for directory in safe_to_clean:
            if os.path.exists(directory):
                try:
                    # For cache directories, just delete the contents but keep the directory
                    for item in os.listdir(directory):
                        item_path = os.path.join(directory, item)
                        try:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                                cleaned_count += 1
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                                cleaned_count += 1
                        except Exception as e:
                            log(f"Failed to clean {item_path}: {e}", 0)
                except Exception as e:
                    log(f"Failed to clean {directory}: {e}", 0)
        
        # Delete log files that can get very large
        log_files = [
            os.path.join(profile_path, 'chrome_debug.log'),
            os.path.join(profile_path, 'Default', 'Log Files'),
            os.path.join(profile_path, 'Default', 'Network Action Predictor'),
        ]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    if os.path.isfile(log_file):
                        os.remove(log_file)
                    else:
                        shutil.rmtree(log_file)
                except Exception as e:
                    log(f"Failed to remove log file {log_file}: {e}", 0)
        
        last_cleanup_time = time.time()
        log(f"Chrome profile cleanup completed. Cleaned {cleaned_count} items, freeing approximately {total_size_before/(1024*1024):.1f}MB", 1)
        
    except Exception as e:
        log(f"Error during Chrome profile cleanup: {e}", 2)

def check_system_resources():
    """Check if system has enough resources to continue operation."""
    try:
        memory = psutil.virtual_memory()
        if memory.percent > 70:  # More aggressive memory monitoring
            log(f"High memory usage detected ({memory.percent}%). Performing cleanup.", 2)
            
            # Try to free up memory
            gc.collect()  
            
            if memory.percent > 85:  # Earlier intervention
                log("Critical memory level. Restarting browser.", 2)
                global driver
                if driver:
                    try:
                        driver.quit()
                        driver = None
                        time.sleep(2)
                        driver = initialize_driver()
                        if driver:
                            driver.get("https://www.midjourney.com/imagine")
                    except Exception as e:
                        log(f"Error restarting browser: {e}", 3)
        
        return True
    except Exception as e:
        log(f"Error checking resources: {e}", 3)
        return True

def automate_submission():
    global current_row, automation_timeout, browser_last_used

    if not (automation_active and not paused and current_row < rows_to_process):
        return
        
    try:
        if not check_system_resources() or not check_browser_health():
            pause_automation()
            return
        
        browser_last_used = time.time()
        
        # OPTIMIZED: Process in smaller batches to prevent UI blocking and reduce memory usage
        batch_size = min(2, rate_limit_per_minute)  # Smaller batches
        
        for _ in range(batch_size):
            if not (automation_active and not paused and current_row < rows_to_process):
                break
                
            if not check_rate_limit():
                break
                
            submit_next_row()
            
            # OPTIMIZED: Shorter delays
            if automation_timeout > 0:
                time.sleep(min(automation_timeout / 1000, 2))  
        
        # OPTIMIZED: More frequent and aggressive garbage collection
        gc.collect()
        
        # Additional memory cleanup every 10 submissions
        if current_row % 10 == 0:
            # Clean up any lingering pandas objects
            for obj in gc.get_objects():
                if isinstance(obj, pd.DataFrame) and obj._is_copy is None:
                    try:
                        del obj
                    except:
                        pass
            gc.collect()
        
        if current_row < rows_to_process and automation_active and not paused:
            # OPTIMIZED: Shorter scheduling interval
            wait_time = max(5, 60 / rate_limit_per_minute)  
            root.after(int(wait_time * 1000), automate_submission)
        else:
            stop_automation()
            log("Automation completed", 1)
            
    except Exception as e:
        log(f"Automation error: {str(e)}", 3)
        pause_automation()

def close_app():
    global driver
    if driver:
        try:
            driver.quit()
        except Exception as e:
            log(f"Error closing browser: {e}")
    root.destroy()

def clear_log():
    status_log.delete(1.0, tk.END)

def update_timeout():
    global automation_timeout
    try:
        automation_timeout = int(timeout_entry.get())
        log(f"Timeout updated to: {automation_timeout} ms")
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for timeout.")

def update_rate_limit():
    global rate_limit_per_minute
    try:
        new_limit = int(rate_limit_entry.get())
        if new_limit > 0:
            rate_limit_per_minute = new_limit
            log(f"Rate limit updated to: {rate_limit_per_minute} prompts per minute")
        else:
            messagebox.showerror("Error", "Please enter a positive integer for rate limit.")
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for rate limit.")

def optimize_for_macos():
    """Apply macOS-specific optimizations"""
    try:
        # Set lower memory thresholds for macOS
        global browser_idle_timeout
        browser_idle_timeout = 180  # More aggressive browser closing on macOS (3 minutes)
        
        # Use mach_absolute_time for more precise timing on macOS
        if hasattr(time, 'mach_absolute_time'):
            log("Using macOS high-precision timer", 0)
        
        # Reduce Chrome memory footprint on macOS
        if 'darwin' in sys.platform:
            log("Applying macOS-specific optimizations", 1)
            
            # Set process priority (nice value) to be more cooperative
            try:
                os.nice(10)  # Lower priority to be nicer to system
            except:
                pass
                
            # More aggressive garbage collection on macOS
            gc.set_threshold(100, 5, 5)  # More frequent collection
            
            # Disable unnecessary Chrome features on macOS
            global driver
            if driver:
                try:
                    # Disable hardware acceleration which can use more memory on macOS
                    driver.execute_script("""
                        if (window.chrome && window.chrome.app) {
                            chrome.app.window.current().disableHardwareAcceleration();
                        }
                    """)
                except:
                    pass
    except Exception as e:
        log(f"Failed to apply macOS optimizations: {e}", 2)

def force_cleanup():
    """Manually trigger cleanup of resources to improve performance."""
    log("Manual cleanup triggered. Cleaning up resources...", 1)
    
    # Force garbage collection
    gc.collect()
    
    # Clean Chrome profile
    cleanup_chrome_profile()
    
    # Check if browser needs restarting
    global driver
    if driver:
        try:
            process = psutil.Process(driver.service.process.pid)
            driver_memory = process.memory_info().rss / (1024 * 1024)  
            
            if driver_memory > 300:  # More aggressive browser memory management
                log(f"Chrome is using {driver_memory:.1f}MB. Restarting browser to free memory.", 1)
                current_url = driver.current_url
                driver.quit()
                driver = None
                time.sleep(2)  
                driver = initialize_driver()
                if driver:
                    driver.get(current_url)
                    log("Browser restarted successfully", 1)
            else:
                log(f"Chrome memory usage is acceptable ({driver_memory:.1f}MB)", 0)
        except Exception as e:
            log(f"Error checking browser memory: {e}", 2)
    
    # Clean up Python memory more aggressively
    for obj in gc.get_objects():
        if isinstance(obj, (pd.DataFrame, pd.Series)):
            try:
                if hasattr(obj, '_is_copy') and obj._is_copy is None:
                    del obj
            except:
                pass
    
    # Clear any cached data
    if hasattr(pd, 'core'):
        try:
            pd.core.common._clear_cache()
        except:
            pass
    
    memory = psutil.virtual_memory()
    log(f"Cleanup complete. Current memory usage: {memory.percent}%", 1)



# --- UI Layout using Grid ---
#Main container Frame
main_frame = tk.Frame(root, padx=10, pady=10)
main_frame.pack(fill=tk.BOTH, expand=True)

#Control Frame
control_frame = tk.LabelFrame(main_frame,text="Controllers", padx=5, pady=5)
control_frame.grid(row=0, column=0, sticky="ew", padx=10,pady=10)


load_button = tk.Button(control_frame, text="Load CSV", command=load_csv)
load_button.grid(row=0, column=0, pady=5, padx=5,sticky="ew")

open_page_button = tk.Button(control_frame, text="Open Web Page", command=open_web_page)
open_page_button.grid(row=0, column=1, pady=5,padx=5,sticky="ew")

submit_button = tk.Button(control_frame, text="Submit Next Row", command=submit_next_row, state=tk.DISABLED)
submit_button.grid(row=1, column=0, pady=5,padx=5,sticky="ew")

start_button = tk.Button(control_frame, text="Start Automation", command=start_automation, state=tk.DISABLED)
start_button.grid(row=1, column=1, pady=5, padx=5,sticky="ew")

pause_button = tk.Button(control_frame, text="Pause Automation", command=pause_automation,state=tk.DISABLED)
pause_button.grid(row=2, column=0, pady=5,padx=5,sticky="ew")

resume_button = tk.Button(control_frame, text="Resume Automation", command=resume_automation, state=tk.DISABLED)
resume_button.grid(row=2, column=1,pady=5,padx=5,sticky="ew")

# Add cleanup button
cleanup_button = tk.Button(control_frame, text="Force Cleanup", command=force_cleanup)
cleanup_button.grid(row=3, column=0, pady=5, padx=5, sticky="ew")

stop_button = tk.Button(control_frame, text="Stop Automation", command=stop_automation)
stop_button.grid(row=3, column=1, pady=5, padx=5, sticky="ew")



#Settings Frame
settings_frame = tk.LabelFrame(main_frame, text="Settings", padx=5, pady=5)
settings_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=10)

# Row Count Input
row_count_label = tk.Label(settings_frame, text="Rows to Process:")
row_count_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

row_count_entry = tk.Entry(settings_frame, state=tk.DISABLED)
row_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

# Encoding selection
encoding_label = tk.Label(settings_frame, text="CSV Encoding:")
encoding_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")

encoding_options = ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']
encoding_dropdown = ttk.Combobox(settings_frame, textvariable=selected_encoding, values=encoding_options, state="readonly")
encoding_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

# Timeout Input
timeout_label = tk.Label(settings_frame, text="Timeout (ms):")
timeout_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")

timeout_entry = tk.Entry(settings_frame)
timeout_entry.insert(0, str(automation_timeout))  
timeout_entry.grid(row=2, column=1, padx=5, pady=5, sticky="ew")

update_timeout_button = tk.Button(settings_frame, text="Update Timeout", command=update_timeout)
update_timeout_button.grid(row=2, column=2, padx=5, pady=5,sticky="ew")

# Rate limit settings
rate_limit_label = tk.Label(settings_frame, text="Prompts Per Minute:")
rate_limit_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")

rate_limit_entry = tk.Entry(settings_frame)
rate_limit_entry.insert(0, str(rate_limit_per_minute))  
rate_limit_entry.grid(row=3, column=1, padx=5, pady=5, sticky="ew")

update_rate_button = tk.Button(settings_frame, text="Update Rate", command=update_rate_limit)
update_rate_button.grid(row=3, column=2, padx=5, pady=5, sticky="ew")

# Delete after sent checkbox
delete_checkbox = tk.Checkbutton(settings_frame, text="Delete Row After Sent", variable=delete_after_sent)
delete_checkbox.grid(row=4, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# AR feature checkbox
ar_checkbox = tk.Checkbutton(settings_frame, text="Use Aspect Ratio (--ar) Feature", variable=use_ar_feature)
ar_checkbox.grid(row=5, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# Log level selector
log_level_label = tk.Label(settings_frame, text="Log Level:")
log_level_label.grid(row=6, column=0, padx=5, pady=5, sticky="w")

log_levels = ["Debug", "Info", "Warning", "Error"]
log_level_dropdown = ttk.Combobox(settings_frame, textvariable=log_level_var, values=log_levels, state="readonly")
log_level_dropdown.grid(row=6, column=1, padx=5, pady=5, sticky="ew")
log_level_dropdown.bind("<<ComboboxSelected>>", lambda e: set_log_level())

# Clear log button.
clear_button = tk.Button(main_frame, text="Clear Log", command=clear_log)
clear_button.grid(row=1,column=0,sticky="ew", padx=10,pady=5)


close_button = tk.Button(main_frame, text="Close", command=close_app)
close_button.grid(row=1,column=1, sticky="ew", padx=10,pady=5)

# Stats Frame
stats_frame = tk.LabelFrame(main_frame, text="Prompt Statistics", padx=5, pady=5)
stats_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=10, pady=5)

# Today's prompts
prompts_today_label = tk.Label(stats_frame, text="Prompts Today:")
prompts_today_label.grid(row=0, column=0, padx=5, pady=2, sticky="w")
prompts_today_value = tk.Label(stats_frame, text="0")
prompts_today_value.grid(row=0, column=1, padx=5, pady=2, sticky="w")

# Total prompts
prompts_total_label = tk.Label(stats_frame, text="Total Prompts:")
prompts_total_label.grid(row=0, column=2, padx=5, pady=2, sticky="w")
prompts_total_value = tk.Label(stats_frame, text="0")
prompts_total_value.grid(row=0, column=3, padx=5, pady=2, sticky="w")

# Last prompt time
last_prompt_time_label = tk.Label(stats_frame, text="Last Prompt:")
last_prompt_time_label.grid(row=1, column=0, padx=5, pady=2, sticky="w")
last_prompt_time_value = tk.Label(stats_frame, text="Never")
last_prompt_time_value.grid(row=1, column=1, padx=5, pady=2, sticky="w")

# Current rate
rate_label = tk.Label(stats_frame, text="Current Rate:")
rate_label.grid(row=1, column=2, padx=5, pady=2, sticky="w")
rate_display = tk.Label(stats_frame, text=f"0/{rate_limit_per_minute} per minute")
rate_display.grid(row=1, column=3, padx=5, pady=2, sticky="w")

# Progress bar
progress_frame = tk.Frame(stats_frame)
progress_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky="ew")
progress_bar = ttk.Progressbar(progress_frame, orient="horizontal", length=100, mode="determinate")
progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
progress_text = tk.Label(progress_frame, text="0.0% (0/0)")
progress_text.pack(side=tk.RIGHT, padx=5)

# Status log area - further reduced for memory efficiency
status_log = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, height=6, maxundo=10)  # Reduced height and undo levels
status_log.grid(row=3, column=0, columnspan=2, pady=10, padx=10, sticky="nsew")

# Configure row and column resizing
main_frame.columnconfigure(0, weight=1)
main_frame.columnconfigure(1, weight=1)
main_frame.rowconfigure(3, weight=1) 
settings_frame.columnconfigure(1, weight=1)
stats_frame.columnconfigure((0,1,2,3), weight=1)

def update_ui_periodically():
    """Periodic UI updates to prevent freezing with memory optimization"""
    try:
        root.update_idletasks()
        update_prompt_stats()  # Update prompt statistics
        
        # Periodic memory cleanup every 30 seconds
        if not hasattr(update_ui_periodically, 'last_cleanup'):
            update_ui_periodically.last_cleanup = time.time()
        
        if time.time() - update_ui_periodically.last_cleanup > 30:
            gc.collect()
            update_ui_periodically.last_cleanup = time.time()
            
            # Log memory usage periodically
            try:
                memory = psutil.virtual_memory()
                if memory.percent > 60:  # Log if memory usage is getting high
                    log(f"Memory usage: {memory.percent}%", 0)
            except:
                pass
        
        root.after(200, update_ui_periodically)  # Reduced frequency to 200ms for better performance
    except Exception as e:
        log(f"UI update error: {e}", 2)
        root.after(500, update_ui_periodically)  # Fallback with longer interval

# Load prompt history on startup
load_prompt_history()

# Apply macOS optimizations if on macOS
if 'darwin' in sys.platform:
    optimize_for_macos()

# Start periodic UI updates
root.after(100, update_ui_periodically)

root.mainloop()