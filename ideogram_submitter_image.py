import os
import sys
import time
import random
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from DrissionPage import ChromiumPage, ChromiumOptions
import threading
import gc
from typing import Optional, List
from concurrent.futures import ThreadPoolExecutor
import shutil
import pyperclip
from PIL import Image, ImageTk
import io
import base64
import pya<PERSON>gui

# Global variables
page = None
image_files: Optional[List[str]] = None
current_index = 0
total_files = 0
automation_active = False
paused = False
automation_timeout = 5000
files_to_process = 0
loaded_folder_path = None
browser_last_used = time.time()
browser_idle_timeout = 300
log_level = 2
log_buffer = []
log_last_update = time.time()

# Tkinter root window
root = tk.Tk()
root.title("Ideogram Image Submitter Tool")
root.geometry("700x600")
root.resizable(True, True)

# Initialize Tkinter variables
delete_after_sent = tk.BooleanVar(value=True)
image_preview_enabled = tk.BooleanVar(value=True)
image_extensions = tk.StringVar(value=".jpg .jpeg .png .gif .bmp .webp")

# Global variable to keep reference to the image
current_preview_image = None

def log(message, level=1):
    """
    Logs a message to the status log and the console.
    Levels: 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
    """
    global log_buffer, log_last_update

    if level < log_level:
        return

    level_prefixes = {0: "[DEBUG] ", 1: "", 2: "[WARNING] ", 3: "[ERROR] "}
    prefixed_message = f"{level_prefixes[level]}{message}"

    print(prefixed_message)

    if level >= 2:  # WARNING and ERROR - immediate update
        if threading.current_thread() is threading.main_thread():
            status_log.insert(tk.END, prefixed_message + '\n')
            status_log.see(tk.END)
            if status_log.index(tk.END).split('.')[0] > '100':
                status_log.delete(1.0, "50.0")
        else:
            root.after(0, lambda: _immediate_log_update(prefixed_message))
    else:
        log_buffer.append(prefixed_message)
        current_time = time.time()

        if len(log_buffer) >= 5 or current_time - log_last_update > 1.0:
            if threading.current_thread() is threading.main_thread():
                _flush_log_buffer()
            else:
                root.after(0, _flush_log_buffer)

def initialize_browser():
    """Initialize DrissionPage browser with stealth settings"""
    global page
    try:
        options = ChromiumOptions()
        
        # Set custom profile path for Ideogram
        user_data_dir = os.path.join(os.path.expanduser("~"), "chrome_profiles", "ideogram_profile")
        os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')
        
        # Stealth settings
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--disable-features=VizDisplayCompositor')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-renderer-backgrounding')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--memory-pressure-off')
        options.set_argument('--max_old_space_size=1024')
        options.set_argument('--disable-blink-features=AutomationControlled')
        options.set_argument('--no-first-run')
        options.set_argument('--no-default-browser-check')
        options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        options.headless(False)

        page = ChromiumPage(addr_or_opts=options)
        
        # Additional stealth measures
        page.run_js('''
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            window.chrome = {
                runtime: {},
            };
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        ''')

        log("Browser initialized successfully with custom profile for Ideogram")
        return True

    except Exception as e:
        log(f"Failed to initialize browser: {e}", 3)
        return False

def navigate_to_ideogram():
    """Navigate to Ideogram with human-like behavior"""
    global page
    try:
        if page is None:
            if not initialize_browser():
                return False
                
        if page is not None:  # Double-check after initialization
            log("Navigating to Ideogram...")
            page.get('https://ideogram.ai/t/explore')

            # Wait for page to load
            time.sleep(random.uniform(3, 5))

            # Check if textarea is present using the XPath
            textarea = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div/div/div[2]/div/textarea[1]', timeout=10)
            
            if textarea:
                log("Successfully navigated to Ideogram")
                return True
            else:
                log("Could not find textarea", 2)
                return False
        return False

    except Exception as e:
        log(f"Failed to navigate: {e}", 3)
        return False

def refresh_page():
    """Refresh the Ideogram page"""
    global page
    try:
        if page is None:
            return False
            
        log("Refreshing Ideogram page...")
        page.refresh()
        
        # Wait for page to load after refresh
        time.sleep(random.uniform(3, 5))
        
        # Check if textarea is present after refresh
        textarea = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div/div/div[2]/div/textarea[1]', timeout=10)
        
        if textarea:
            log("Successfully refreshed Ideogram page")
            return True
        else:
            log("Could not find textarea after refresh", 2)
            # Try to navigate to Ideogram again if refresh failed
            return navigate_to_ideogram()
            
    except Exception as e:
        log(f"Failed to refresh page: {e}", 3)
        # Try to navigate to Ideogram again if refresh failed
        return navigate_to_ideogram()

def check_image_generation():
    """Check if the image generation is complete"""
    try:
        if page is None:
            return False
            
        # Wait for the loading indicator to disappear
        time.sleep(2)  # Initial wait
        
        max_wait = 60  # Maximum wait time in seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            # Check for loading indicator
            loading = page.ele('xpath://div[contains(@class, "loading")]', timeout=1)
            if not loading:
                # Check for generated image
                image = page.ele('tag:img', timeout=1)
                if image:
                    log("Image generation completed")
                    # Wait for 10 seconds after image generation is detected
                    log("Waiting 10 seconds before proceeding to next image...")
                    time.sleep(10)
                    return True
            time.sleep(2)
            
        log("Image generation timed out", 2)
        return False
        
    except Exception as e:
        log(f"Error checking image generation: {e}", 2)
        return False

def submit_image(image_path):
    """Submit an image to Ideogram"""
    try:
        if page is None:
            return False
            
        # First, check if the image file exists
        if not os.path.exists(image_path):
            log(f"Image file not found: {image_path}", 3)
            return False
            
        # Copy image to clipboard
        try:
            img = Image.open(image_path)
            output = io.BytesIO()
            img.save(output, format='PNG')
            data = output.getvalue()
            
            # Update image preview if enabled
            if image_preview_enabled.get():
                update_image_preview(img)
                
            # Copy to clipboard
            pyperclip.copy(base64.b64encode(data).decode())
            log("Image copied to clipboard")
        except Exception as e:
            log(f"Failed to copy image to clipboard: {e}", 3)
            return False
        
        # Step 1: Find the textarea to paste the image
        textarea = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div/div/div[2]/div/textarea[1]', timeout=10)
        if not textarea:
            log("Could not find textarea", 2)
            return False
            
        # Click the textarea to focus on it
        textarea.click()
        time.sleep(1)
        log("Clicked textarea")
        
        # Paste the image using keyboard shortcut
        # Use command+v on macOS, ctrl+v on Windows/Linux
        if sys.platform == 'darwin':
            pyautogui.hotkey('command', 'v')
        else:
            pyautogui.hotkey('ctrl', 'v')
        time.sleep(1)
        log("Pasted image in textarea")
        
        # Step 2: Find and click the specified button
        action_button = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div[2]/div/div[3]/div[1]/div/button[2]/div', timeout=10)
        if not action_button:
            log("Could not find action button", 2)
            return False
            
        # Click the button
        action_button.click()
        log("Clicked action button")
        time.sleep(5)  # Wait for 5 seconds as specified
        
        # Press Enter to submit
        pyautogui.press('enter')
        log("Pressed Enter to submit")
        
        # Wait for and verify image generation
        if check_image_generation():
            log(f"Successfully submitted image: {os.path.basename(image_path)}")
            return True
        else:
            log("Image generation failed or timed out", 2)
            return False

    except Exception as e:
        log(f"Failed to submit image: {e}", 3)
        return False

def update_image_preview(img):
    """Update the image preview panel with the current image"""
    global current_preview_image
    try:
        # Resize image to fit preview panel (200x200 pixels)
        img.thumbnail((200, 200))
        photo = ImageTk.PhotoImage(img)
        
        # Update preview
        preview_label.config(image=photo)
        # Store reference to prevent garbage collection
        current_preview_image = photo
    except Exception as e:
        log(f"Failed to update image preview: {e}", 2)

def load_folder():
    global image_files, current_index, files_to_process, loaded_folder_path, total_files

    folder_path = filedialog.askdirectory(title="Select Folder with Images")
    if not folder_path:
        return

    try:
        # Get list of all valid image files in the folder
        extensions = image_extensions.get().split()
        image_files = []
        
        for file in os.listdir(folder_path):
            if any(file.lower().endswith(ext) for ext in extensions):
                image_files.append(os.path.join(folder_path, file))
        
        total_files = len(image_files)
        if total_files == 0:
            messagebox.showinfo("No Images Found", f"No images with extensions {extensions} found in the selected folder.")
            return
            
        current_index = 0
        files_to_process = total_files
        loaded_folder_path = folder_path

        file_count_entry.delete(0, tk.END)
        file_count_entry.insert(0, str(total_files))

        # Enable buttons
        submit_button.config(state=tk.NORMAL)
        start_button.config(state=tk.NORMAL)
        file_count_entry.config(state=tk.NORMAL)

        log(f"Loaded folder with {total_files} images from: {folder_path}")
        
        # Show first image preview
        if image_files and image_preview_enabled.get():
            try:
                img = Image.open(image_files[0])
                update_image_preview(img)
            except Exception as e:
                log(f"Failed to preview first image: {e}", 2)
                
        gc.collect()

    except Exception as e:
        messagebox.showerror("Error", f"Failed to load images from folder: {e}")
        log(f"Failed to load folder: {e}", 3)

def get_next_image_path():
    global current_index, image_files

    try:
        if image_files is None or current_index >= len(image_files):
            return None
            
        image_path = image_files[current_index]
        current_index += 1
        return image_path

    except Exception as e:
        log(f"Error getting next image: {e}", 3)
        return None

def submit_next_image():
    global current_index, browser_last_used

    if image_files is None or loaded_folder_path is None:
        log("Please load a folder with images first")
        return False

    browser_last_used = time.time()

    try:
        image_path = get_next_image_path()
        if image_path is None:
            log("No more images to process")
            return False

        # Submit and wait for generation
        success = submit_image(image_path)

        if success:
            if delete_after_sent.get():
                try:
                    # Move the file to a processed subfolder instead of deleting
                    processed_folder = os.path.join(loaded_folder_path, "processed")
                    os.makedirs(processed_folder, exist_ok=True)
                    
                    dest_path = os.path.join(processed_folder, os.path.basename(image_path))
                    shutil.move(image_path, dest_path)
                    log(f"Moved processed image to: {processed_folder}")
                except Exception as e:
                    log(f"Failed to move processed image: {e}", 2)

            log(f"Submitted image {current_index} of {total_files}", 1)
            
            # Refresh the page after successful submission
            refresh_page()
            
            return True
        else:
            log(f"Failed to submit image {current_index}", 3)
            return False

    except Exception as e:
        log(f"Submission failed: {str(e)}", 3)
        return False

def start_automation():
    global automation_active, files_to_process, current_index

    try:
        files_to_process = int(file_count_entry.get())
        if files_to_process <= 0:
            messagebox.showerror("Error", "Please enter a valid positive integer for file count.")
            return
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for file count.")
        return

    if image_files is None:
        log("Please load a folder with images first")
        return

    if not page:
        if not initialize_browser():
            return
        if not navigate_to_ideogram():
            return

    automation_active = True
    start_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    log("Automation started...")

    automation_thread = threading.Thread(target=automate_submission)
    automation_thread.daemon = True
    automation_thread.start()

def automate_submission():
    global current_index, automation_timeout

    while automation_active and not paused and current_index <= files_to_process and current_index <= total_files:
        if submit_next_image():
            if automation_timeout > 0:
                time.sleep(automation_timeout / 1000)
        else:
            break

        gc.collect()

    stop_automation()
    log("Automation completed", 1)

def pause_automation():
    global paused
    paused = True
    log("Automation paused.")
    pause_button.config(state=tk.DISABLED)
    resume_button.config(state=tk.NORMAL)

def resume_automation():
    global paused
    paused = False
    log("Resuming automation...")
    resume_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    automate_submission()

def stop_automation():
    global automation_active
    automation_active = False
    start_button.config(state=tk.NORMAL)
    pause_button.config(state=tk.DISABLED)
    log("Automation stopped.")

def close_app():
    global page, thread_pool
    try:
        automation_active = False
        if page:
            page.quit()
        thread_pool.shutdown(wait=False)
        gc.collect()
    except Exception as e:
        log(f"Error during cleanup: {e}")
    finally:
        root.destroy()

def _immediate_log_update(message):
    """Immediately update log for critical messages"""
    status_log.insert(tk.END, message + '\n')
    status_log.see(tk.END)
    if status_log.index(tk.END).split('.')[0] > '100':
        status_log.delete(1.0, "50.0")

def _flush_log_buffer():
    """Flush the log buffer to the UI with optimized performance."""
    global log_buffer, log_last_update

    if log_buffer:
        messages = '\n'.join(log_buffer) + '\n'
        status_log.insert(tk.END, messages)

        current_lines = int(status_log.index(tk.END).split('.')[0])
        if current_lines > 100:
            status_log.delete(1.0, "50.0")

        status_log.see(tk.END)
        log_buffer.clear()
        log_last_update = time.time()

# Create UI Layout
main_frame = tk.Frame(root, padx=10, pady=10)
main_frame.pack(fill=tk.BOTH, expand=True)

# Control Frame
control_frame = tk.LabelFrame(main_frame, text="Controls", padx=5, pady=5)
control_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)

load_button = tk.Button(control_frame, text="Load Image Folder", command=load_folder)
load_button.grid(row=0, column=0, pady=5, padx=5, sticky="ew")

open_page_button = tk.Button(control_frame, text="Open Ideogram", command=lambda: threading.Thread(target=navigate_to_ideogram).start())
open_page_button.grid(row=0, column=1, pady=5, padx=5, sticky="ew")

submit_button = tk.Button(control_frame, text="Submit Next Image", command=submit_next_image, state=tk.DISABLED)
submit_button.grid(row=1, column=0, pady=5, padx=5, sticky="ew")

start_button = tk.Button(control_frame, text="Start Automation", command=start_automation, state=tk.DISABLED)
start_button.grid(row=1, column=1, pady=5, padx=5, sticky="ew")

pause_button = tk.Button(control_frame, text="Pause", command=pause_automation, state=tk.DISABLED)
pause_button.grid(row=2, column=0, pady=5, padx=5, sticky="ew")

resume_button = tk.Button(control_frame, text="Resume", command=resume_automation, state=tk.DISABLED)
resume_button.grid(row=2, column=1, pady=5, padx=5, sticky="ew")

# Settings Frame
settings_frame = tk.LabelFrame(main_frame, text="Settings", padx=5, pady=5)
settings_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=5)

# File count input
file_count_label = tk.Label(settings_frame, text="Images to Process:")
file_count_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

file_count_entry = tk.Entry(settings_frame, state=tk.DISABLED)
file_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

# File extensions
extensions_label = tk.Label(settings_frame, text="Image Extensions:")
extensions_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")

extensions_entry = tk.Entry(settings_frame, textvariable=image_extensions)
extensions_entry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

# Move after sent checkbox
delete_checkbox = tk.Checkbutton(settings_frame, text="Move to 'processed' After Sent", variable=delete_after_sent)
delete_checkbox.grid(row=2, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# Preview checkbox
preview_checkbox = tk.Checkbutton(settings_frame, text="Show Image Preview", variable=image_preview_enabled)
preview_checkbox.grid(row=3, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# Image preview
preview_frame = tk.LabelFrame(main_frame, text="Image Preview", padx=5, pady=5)
preview_frame.grid(row=1, column=1, sticky="nsew", padx=10, pady=5)

# Create a label for image preview with placeholder
preview_label = tk.Label(preview_frame, text="No image loaded", width=20, height=10)
preview_label.pack(fill=tk.BOTH, expand=True)

# Status Log
log_frame = tk.LabelFrame(main_frame, text="Status Log", padx=5, pady=5)
log_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)

status_log = scrolledtext.ScrolledText(log_frame, height=15)
status_log.pack(fill=tk.BOTH, expand=True)

# Configure grid weights
main_frame.grid_columnconfigure(1, weight=1)
main_frame.grid_rowconfigure(1, weight=1)

# Initialize thread pool
thread_pool = ThreadPoolExecutor(max_workers=2)

# Bind close event
root.protocol("WM_DELETE_WINDOW", close_app)

if __name__ == "__main__":
    log("Ideogram Image Submitter Tool started")
    log("Load a folder with images and open Ideogram to begin")
    root.mainloop() 