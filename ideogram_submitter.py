import os
import time
import random
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from DrissionPage import ChromiumPage, ChromiumOptions
import threading
import gc
from typing import Optional, Iterator
import queue
from concurrent.futures import ThreadPoolExecutor
import shutil

# Global variables
page = None
csv_iterator: Optional[Iterator] = None
current_chunk = None
current_row = 0
total_rows = 0
chunk_size = 100
automation_active = False
paused = False
automation_timeout = 5000
rows_to_process = 0
loaded_csv_path = None
rate_limit_count = 0
rate_limit_start_time = time.time()
rate_limit_per_minute = 3
log_buffer = []
log_last_update = time.time()
browser_last_used = time.time()
browser_idle_timeout = 300
log_level = 2
rate_limit_queue = []
rate_limit_lock = threading.Lock()

# Prompt tracking variables
prompts_sent_today = 0
prompts_sent_total = 0
prompt_history = []
last_prompt_time = None
prompt_tracking_file = "prompt_history.csv"

# Tkinter root window
root = tk.Tk()
root.title("Ideogram CSV Submitter Tool")
root.geometry("700x600")
root.resizable(True, True)

# Initialize Tkinter variables
delete_after_sent = tk.BooleanVar(value=True)
selected_encoding = tk.StringVar(value="utf-8")
log_level_var = tk.StringVar(value="Warning")
prompt_prefix = tk.StringVar(value="")
prompt_suffix = tk.StringVar(value="")

class CSVHandler:
    @staticmethod
    def get_chunk_iterator(file_path: str, encoding: str, chunk_size: int = 100) -> tuple[Iterator, int]:
        """Returns a chunk iterator and total number of rows for the CSV file."""
        with open(file_path, 'r', encoding=encoding) as f:
            total_rows = sum(1 for _ in f) - 1  # subtract header

        # First read the header to get column names
        header_df = pd.read_csv(file_path, nrows=0, encoding=encoding)
        first_col = header_df.columns[0]

        iterator = pd.read_csv(
            file_path,
            encoding=encoding,
            chunksize=chunk_size,
            low_memory=True,
            engine='c',
            dtype=str,  # Use str type for all columns
            na_filter=False
        )

        return iterator, total_rows

    @staticmethod
    def save_progress(file_path: str, processed_rows: list, encoding: str):
        """Save progress by removing processed rows from the CSV."""
        try:
            if not os.path.exists(file_path):
                log(f"Error: CSV file not found at {file_path}", 2)
                return False

            # Create backup
            backup_path = f"{file_path}.bak"
            shutil.copy2(file_path, backup_path)

            # Process in chunks
            temp_file = f"{file_path}.temp"
            header_written = False

            for chunk in pd.read_csv(file_path, encoding=encoding, chunksize=50):
                chunk = chunk[~chunk.index.isin(processed_rows)]
                if not chunk.empty:
                    chunk.to_csv(temp_file, mode='a', header=not header_written, index=False, encoding=encoding)
                    header_written = True

            if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                os.replace(temp_file, file_path)
                log(f"Successfully deleted {len(processed_rows)} row(s) from CSV", 1)
                return True

            return False

        except Exception as e:
            log(f"Error saving progress: {e}", 3)
            return False

def log(message, level=1):
    """
    Logs a message to the status log and the console.
    Levels: 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
    """
    global log_buffer, log_last_update

    if level < log_level:
        return

    level_prefixes = {0: "[DEBUG] ", 1: "", 2: "[WARNING] ", 3: "[ERROR] "}
    prefixed_message = f"{level_prefixes[level]}{message}"

    print(prefixed_message)

    if level >= 2:  # WARNING and ERROR - immediate update
        if threading.current_thread() is threading.main_thread():
            status_log.insert(tk.END, prefixed_message + '\n')
            status_log.see(tk.END)
            if status_log.index(tk.END).split('.')[0] > '100':
                status_log.delete(1.0, "50.0")
        else:
            root.after(0, lambda: _immediate_log_update(prefixed_message))
    else:
        log_buffer.append(prefixed_message)
        current_time = time.time()

        if len(log_buffer) >= 5 or current_time - log_last_update > 1.0:
            if threading.current_thread() is threading.main_thread():
                _flush_log_buffer()
            else:
                root.after(0, _flush_log_buffer)

def initialize_browser():
    """Initialize DrissionPage browser with stealth settings"""
    global page
    try:
        options = ChromiumOptions()
        
        # Set custom profile path for Ideogram
        user_data_dir = os.path.join(os.path.expanduser("~"), "chrome_profiles", "ideogram_profile")
        os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')
        
        # Stealth settings
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--disable-features=VizDisplayCompositor')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-renderer-backgrounding')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--memory-pressure-off')
        options.set_argument('--max_old_space_size=1024')
        options.set_argument('--disable-blink-features=AutomationControlled')
        options.set_argument('--no-first-run')
        options.set_argument('--no-default-browser-check')
        options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        options.headless(False)

        page = ChromiumPage(addr_or_opts=options)
        
        # Additional stealth measures
        page.run_js('''
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            window.chrome = {
                runtime: {},
            };
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        ''')

        log("Browser initialized successfully with custom profile for Ideogram")
        return True

    except Exception as e:
        log(f"Failed to initialize browser: {e}", 3)
        return False

def navigate_to_ideogram():
    """Navigate to Ideogram with human-like behavior"""
    global page
    try:
        if page is None:
            if not initialize_browser():
                return False
                
        if page is not None:  # Double-check after initialization
            log("Navigating to Ideogram...")
            page.get('https://ideogram.ai/t/explore')

            # Wait for page to load
            time.sleep(random.uniform(3, 5))

            # Check if input field is present using the XPath
            input_field = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div[1]/div/div[1]', timeout=10)
            
            if input_field:
                log("Successfully navigated to Ideogram")
                return True
            else:
                log("Could not find input field", 2)
                return False
        return False

    except Exception as e:
        log(f"Failed to navigate: {e}", 3)
        return False

def refresh_page():
    """Refresh the Ideogram page"""
    global page
    try:
        if page is None:
            return False
            
        log("Refreshing Ideogram page...")
        page.refresh()
        
        # Wait for page to load after refresh
        time.sleep(random.uniform(3, 5))
        
        # Check if input field is present after refresh
        input_field = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div[1]/div/div[1]', timeout=10)
        
        if input_field:
            log("Successfully refreshed Ideogram page")
            return True
        else:
            log("Could not find input field after refresh", 2)
            # Try to navigate to Ideogram again if refresh failed
            return navigate_to_ideogram()
            
    except Exception as e:
        log(f"Failed to refresh page: {e}", 3)
        # Try to navigate to Ideogram again if refresh failed
        return navigate_to_ideogram()

def check_image_generation():
    """Check if the image generation is complete"""
    try:
        if page is None:
            return False
            
        # Wait for the loading indicator to disappear
        time.sleep(2)  # Initial wait
        
        max_wait = 60  # Maximum wait time in seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            # Check for loading indicator
            loading = page.ele('xpath://div[contains(@class, "loading")]', timeout=1)
            if not loading:
                # Check for generated image
                image = page.ele('tag:img', timeout=1)
                if image:
                    log("Image generation completed")
                    # Wait for 10 seconds after image generation is detected
                    log("Waiting 10 seconds before proceeding to next prompt...")
                    time.sleep(10)
                    return True
            time.sleep(2)
            
        log("Image generation timed out", 2)
        return False
        
    except Exception as e:
        log(f"Error checking image generation: {e}", 2)
        return False

def submit_prompt(prompt_text):
    """Submit a prompt to Ideogram"""
    try:
        if page is None:
            return False
            
        # Find input field
        input_field = page.ele('xpath:/html/body/div[1]/div[2]/div[3]/div[2]/div[3]/div[1]/div[1]/div/div[1]', timeout=10)
        
        if not input_field:
            log("Could not find input field", 2)
            return False

        # Clear existing text with human-like behavior
        input_field.click()
        time.sleep(random.uniform(0.2, 0.5))
        input_field.clear()
        time.sleep(random.uniform(0.1, 0.3))

        # Input the prompt
        input_field.input(prompt_text)
        time.sleep(random.uniform(0.5, 1.0))

        # Press Enter
        input_field.input('\n')
        
        # Wait for and verify image generation
        if check_image_generation():
            log(f"Successfully submitted and generated: {prompt_text[:50]}...")
            return True
        else:
            log("Image generation failed or timed out", 2)
            return False

    except Exception as e:
        log(f"Failed to submit prompt: {e}", 3)
        return False

def load_csv():
    global csv_iterator, current_chunk, current_row, rows_to_process, loaded_csv_path, total_rows

    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        return

    try:
        encoding = selected_encoding.get()
        csv_iterator, total_rows = CSVHandler.get_chunk_iterator(file_path, encoding, chunk_size)
        
        if csv_iterator is not None:
            current_chunk = next(csv_iterator)
            current_row = 1
            rows_to_process = total_rows
            loaded_csv_path = file_path

            row_count_entry.delete(0, tk.END)
            row_count_entry.insert(0, str(total_rows))

            # Enable buttons
            submit_button.config(state=tk.NORMAL)
            start_button.config(state=tk.NORMAL)
            row_count_entry.config(state=tk.NORMAL)

            log(f"Loaded CSV with {encoding} encoding - Total rows: {total_rows}")
            gc.collect()
        else:
            raise ValueError("Failed to create CSV iterator")

    except Exception as e:
        messagebox.showerror("Error", f"Failed to load CSV file: {e}")
        log(f"Failed to load CSV: {e}", 3)

def get_next_row_data():
    global current_chunk, csv_iterator, current_row

    try:
        if current_chunk is None or csv_iterator is None:
            return None
            
        if (current_row-1) >= len(current_chunk):
            try:
                current_chunk = next(csv_iterator)
                current_row = 1
            except StopIteration:
                return None

        # Get the first column's name and use it to access the data
        first_col = current_chunk.columns[0]
        row_data = current_chunk.iloc[current_row-1][first_col]
        current_row += 1

        return str(row_data).strip() if pd.notna(row_data) else None

    except Exception as e:
        log(f"Error getting next row: {e}", 3)
        return None

def submit_next_row():
    global current_row, browser_last_used

    if csv_iterator is None or current_chunk is None or loaded_csv_path is None:
        log("Please load a CSV file first")
        return False

    browser_last_used = time.time()

    try:
        input_value = get_next_row_data()
        if input_value is None:
            log(f"No more data at row {current_row}")
            return False

        # Process the prompt
        input_value = str(input_value).strip().replace('\n', ' ').replace('\r', ' ')
        if not input_value:
            log(f"Empty data at row {current_row}")
            return False

        # Add prefix and suffix
        final_prompt = input_value
        prefix_text = prompt_prefix.get().strip()
        if prefix_text:
            final_prompt = f"{prefix_text} {final_prompt}"
        
        suffix_text = prompt_suffix.get().strip()
        if suffix_text:
            final_prompt = f"{final_prompt} {suffix_text}"

        # Submit and wait for generation
        success = submit_prompt(final_prompt)

        if success:
            if delete_after_sent.get():
                thread_pool.submit(
                    CSVHandler.save_progress,
                    loaded_csv_path,
                    [current_chunk.index[current_row - 2]],
                    selected_encoding.get()
                )

            log(f"Submitted row {current_row}", 1)
            
            # Refresh the page after successful submission
            refresh_page()
            
            return True
        else:
            log(f"Failed to submit row {current_row}", 3)
            return False

    except Exception as e:
        log(f"Submission failed at row {current_row}: {str(e)}", 3)
        return False

def start_automation():
    global automation_active, rows_to_process, current_row

    try:
        rows_to_process = int(row_count_entry.get())
        if rows_to_process <= 0:
            messagebox.showerror("Error", "Please enter a valid positive integer for row count.")
            return
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for row count.")
        return

    if csv_iterator is None:
        log("Please load a CSV file first")
        return

    if not page:
        if not initialize_browser():
            return
        if not navigate_to_ideogram():
            return

    automation_active = True
    start_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    log("Automation started...")
    current_row = 1

    automation_thread = threading.Thread(target=automate_submission)
    automation_thread.daemon = True
    automation_thread.start()

def automate_submission():
    global current_row, automation_timeout

    while automation_active and not paused and current_row <= rows_to_process:
        if submit_next_row():
            if automation_timeout > 0:
                time.sleep(automation_timeout / 1000)
        else:
            break

        gc.collect()

    stop_automation()
    log("Automation completed", 1)

def pause_automation():
    global paused
    paused = True
    log("Automation paused.")
    pause_button.config(state=tk.DISABLED)
    resume_button.config(state=tk.NORMAL)

def resume_automation():
    global paused
    paused = False
    log("Resuming automation...")
    resume_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    automate_submission()

def stop_automation():
    global automation_active
    automation_active = False
    start_button.config(state=tk.NORMAL)
    pause_button.config(state=tk.DISABLED)
    log("Automation stopped.")

def close_app():
    global page, thread_pool
    try:
        automation_active = False
        if page:
            page.quit()
        thread_pool.shutdown(wait=False)
        gc.collect()
    except Exception as e:
        log(f"Error during cleanup: {e}")
    finally:
        root.destroy()

def _immediate_log_update(message):
    """Immediately update log for critical messages"""
    status_log.insert(tk.END, message + '\n')
    status_log.see(tk.END)
    if status_log.index(tk.END).split('.')[0] > '100':
        status_log.delete(1.0, "50.0")

def _flush_log_buffer():
    """Flush the log buffer to the UI with optimized performance."""
    global log_buffer, log_last_update

    if log_buffer:
        messages = '\n'.join(log_buffer) + '\n'
        status_log.insert(tk.END, messages)

        current_lines = int(status_log.index(tk.END).split('.')[0])
        if current_lines > 100:
            status_log.delete(1.0, "50.0")

        status_log.see(tk.END)
        log_buffer.clear()
        log_last_update = time.time()

# Create UI Layout
main_frame = tk.Frame(root, padx=10, pady=10)
main_frame.pack(fill=tk.BOTH, expand=True)

# Control Frame
control_frame = tk.LabelFrame(main_frame, text="Controls", padx=5, pady=5)
control_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)

load_button = tk.Button(control_frame, text="Load CSV", command=load_csv)
load_button.grid(row=0, column=0, pady=5, padx=5, sticky="ew")

open_page_button = tk.Button(control_frame, text="Open Ideogram", command=lambda: threading.Thread(target=navigate_to_ideogram).start())
open_page_button.grid(row=0, column=1, pady=5, padx=5, sticky="ew")

submit_button = tk.Button(control_frame, text="Submit Next Row", command=submit_next_row, state=tk.DISABLED)
submit_button.grid(row=1, column=0, pady=5, padx=5, sticky="ew")

start_button = tk.Button(control_frame, text="Start Automation", command=start_automation, state=tk.DISABLED)
start_button.grid(row=1, column=1, pady=5, padx=5, sticky="ew")

pause_button = tk.Button(control_frame, text="Pause", command=pause_automation, state=tk.DISABLED)
pause_button.grid(row=2, column=0, pady=5, padx=5, sticky="ew")

resume_button = tk.Button(control_frame, text="Resume", command=resume_automation, state=tk.DISABLED)
resume_button.grid(row=2, column=1, pady=5, padx=5, sticky="ew")

# Settings Frame
settings_frame = tk.LabelFrame(main_frame, text="Settings", padx=5, pady=5)
settings_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=5)

# Row count input
row_count_label = tk.Label(settings_frame, text="Rows to Process:")
row_count_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

row_count_entry = tk.Entry(settings_frame, state=tk.DISABLED)
row_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

# Encoding selection
encoding_label = tk.Label(settings_frame, text="CSV Encoding:")
encoding_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")

encoding_options = ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']
encoding_dropdown = ttk.Combobox(settings_frame, textvariable=selected_encoding, values=encoding_options, state="readonly")
encoding_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

# Delete after sent checkbox
delete_checkbox = tk.Checkbutton(settings_frame, text="Delete Row After Sent", variable=delete_after_sent)
delete_checkbox.grid(row=2, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# Prompt prefix/suffix
prefix_label = tk.Label(settings_frame, text="Prompt Prefix:")
prefix_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")

prefix_entry = tk.Entry(settings_frame, textvariable=prompt_prefix)
prefix_entry.grid(row=3, column=1, padx=5, pady=5, sticky="ew")

suffix_label = tk.Label(settings_frame, text="Prompt Suffix:")
suffix_label.grid(row=4, column=0, padx=5, pady=5, sticky="w")

suffix_entry = tk.Entry(settings_frame, textvariable=prompt_suffix)
suffix_entry.grid(row=4, column=1, padx=5, pady=5, sticky="ew")

# Status Log
log_frame = tk.LabelFrame(main_frame, text="Status Log", padx=5, pady=5)
log_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=10, pady=5)

status_log = scrolledtext.ScrolledText(log_frame, height=15)
status_log.pack(fill=tk.BOTH, expand=True)

# Configure grid weights
main_frame.grid_columnconfigure(1, weight=1)
main_frame.grid_rowconfigure(1, weight=1)

# Initialize thread pool
thread_pool = ThreadPoolExecutor(max_workers=2)

# Bind close event
root.protocol("WM_DELETE_WINDOW", close_app)

if __name__ == "__main__":
    log("Ideogram CSV Submitter Tool started")
    log("Load a CSV file and open Ideogram to begin")
    root.mainloop() 