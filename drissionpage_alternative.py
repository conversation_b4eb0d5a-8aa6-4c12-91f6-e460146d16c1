import os
import time
import random
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from DrissionPage import ChromiumPage, ChromiumOptions
import threading
import gc
from typing import Optional, Iterator
import shutil
import psutil
import sys

# Global variables
page = None
csv_iterator: Optional[Iterator] = None
current_chunk = None
current_row = 0
total_rows = 0
chunk_size = 100
automation_active = False
paused = False
aspect_ratios = ["16:9", "4:3", "1:1", "21:9", "3:2"]
automation_timeout = 5000
rows_to_process = 0
loaded_csv_path = None
rate_limit_count = 0
rate_limit_start_time = time.time()
rate_limit_per_minute = 3
log_buffer = []
log_last_update = time.time()
browser_last_used = time.time()
browser_idle_timeout = 300
log_level = 2
rate_limit_queue = []
rate_limit_lock = threading.Lock()

# Prompt tracking variables
prompts_sent_today = 0
prompts_sent_total = 0
prompt_history = []
last_prompt_time = None
prompt_tracking_file = "prompt_history.csv"

# Tkinter root window
root = tk.Tk()
root.title("DrissionPage CSV Submitter Tool")
root.geometry("700x600")
root.resizable(True, True)

# Initialize Tkinter variables after root
delete_after_sent = tk.BooleanVar(value=True)
use_ar_feature = tk.BooleanVar(value=True)
selected_encoding = tk.StringVar(value="utf-8")
log_level_var = tk.StringVar(value="Warning")
prompt_prefix = tk.StringVar(value="")
prompt_suffix = tk.StringVar(value="")

class CSVHandler:
    @staticmethod
    def get_chunk_iterator(file_path: str, encoding: str, chunk_size: int = 100) -> tuple[Iterator, int]:
        """Returns a chunk iterator and total number of rows for the CSV file."""
        with open(file_path, 'r', encoding=encoding) as f:
            total_rows = sum(1 for _ in f) - 1  # subtract header

        iterator = pd.read_csv(
            file_path,
            encoding=encoding,
            chunksize=chunk_size,
            low_memory=True,
            engine='c',
            usecols=['input_data'],
            dtype={'input_data': 'string'},
            na_filter=False
        )

        return iterator, total_rows

    @staticmethod
    def save_progress(file_path: str, processed_rows: list, encoding: str, max_retries: int = 3):
        """Save progress by removing processed rows from the CSV with retry mechanism."""
        for retry in range(max_retries):
            try:
                if not os.path.exists(file_path):
                    log(f"Error: CSV file not found at {file_path}", 2)
                    return False

                backup_path = f"{file_path}.bak"
                try:
                    shutil.copy2(file_path, backup_path)
                except Exception as e:
                    log(f"Warning: Could not create backup file: {e}", 2)

                chunk_size = 50
                temp_file = f"{file_path}.temp"
                header_written = False

                for chunk in pd.read_csv(file_path, encoding=encoding, chunksize=chunk_size, low_memory=True):
                    chunk = chunk[~chunk.index.isin(processed_rows)]

                    if not chunk.empty:
                        chunk.to_csv(temp_file, mode='a', header=not header_written, index=False, encoding=encoding)
                        header_written = True

                    del chunk
                    gc.collect()

                if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                    try:
                        pd.read_csv(temp_file, encoding=encoding, nrows=1)
                        os.replace(temp_file, file_path)
                        log(f"Successfully deleted {len(processed_rows)} row(s) from CSV", 1)
                        return True
                    except Exception as e:
                        log(f"Error: Temp CSV file is invalid: {e}", 3)
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                        if retry < max_retries - 1:
                            log(f"Retrying deletion (attempt {retry + 2}/{max_retries})...", 1)
                            time.sleep(1)
                            continue
                        return False
                else:
                    log(f"Error: Temp CSV file is missing or empty", 3)
                    return False

                return True

            except Exception as e:
                log(f"Error saving progress (attempt {retry + 1}/{max_retries}): {e}", 3)
                if retry < max_retries - 1:
                    time.sleep(1)
                    continue

                backup_path = f"{file_path}.bak"
                if os.path.exists(backup_path):
                    try:
                        shutil.copy2(backup_path, file_path)
                        log("Restored CSV file from backup after error", 2)
                    except Exception as restore_error:
                        log(f"Failed to restore CSV from backup: {restore_error}", 3)
                return False

        return False

def log(message, level=1):
    """
    Logs a message to the status log and the console.
    Levels: 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
    """
    global log_buffer, log_last_update

    if level < log_level:
        return

    level_prefixes = {0: "[DEBUG] ", 1: "", 2: "[WARNING] ", 3: "[ERROR] "}
    prefixed_message = f"{level_prefixes[level]}{message}"

    print(prefixed_message)

    if level >= 2:  # WARNING and ERROR - immediate update
        if threading.current_thread() is threading.main_thread():
            status_log.insert(tk.END, prefixed_message + '\n')
            status_log.see(tk.END)
            if status_log.index(tk.END).split('.')[0] > '100':
                status_log.delete(1.0, "50.0")
        else:
            root.after(0, lambda: _immediate_log_update(prefixed_message))
    else:
        log_buffer.append(prefixed_message)
        current_time = time.time()

        if len(log_buffer) >= 5 or current_time - log_last_update > 1.0:
            if threading.current_thread() is threading.main_thread():
                _flush_log_buffer()
            else:
                root.after(0, _flush_log_buffer)

def _immediate_log_update(message):
    """Immediately update log for critical messages"""
    status_log.insert(tk.END, message + '\n')
    status_log.see(tk.END)
    if status_log.index(tk.END).split('.')[0] > '100':
        status_log.delete(1.0, "50.0")

def _flush_log_buffer():
    """Flush the log buffer to the UI with optimized performance."""
    global log_buffer, log_last_update

    if log_buffer:
        # Use deque's efficient operations
        messages = '\n'.join(log_buffer) + '\n'
        status_log.insert(tk.END, messages)

        # Efficient line count management
        current_lines = int(status_log.index(tk.END).split('.')[0])
        if current_lines > 100:  # Increased buffer for better performance
            status_log.delete(1.0, "50.0")

        status_log.see(tk.END)
        log_buffer.clear()
        log_last_update = time.time()

def set_log_level():
    """Update the log level from the dropdown."""
    global log_level
    selected = log_level_var.get()
    if selected == "Debug":
        log_level = 0
    elif selected == "Info":
        log_level = 1
    elif selected == "Warning":
        log_level = 2
    elif selected == "Error":
        log_level = 3
    log(f"Log level set to: {selected}", 1)

def check_rate_limit():
    """
    Enhanced rate limit checker with queue system.
    Returns True if we can proceed, False if we need to wait.
    """
    global rate_limit_queue, rate_limit_start_time, rate_limit_count

    with rate_limit_lock:
        current_time = time.time()

        # Remove timestamps older than 1 minute from queue
        while rate_limit_queue and current_time - rate_limit_queue[0] >= 60:
            rate_limit_queue.pop(0)

        # Check if we're within rate limit
        if len(rate_limit_queue) < rate_limit_per_minute:
            rate_limit_queue.append(current_time)
            remaining = rate_limit_per_minute - len(rate_limit_queue)
            log(f"Rate limit: {len(rate_limit_queue)}/{rate_limit_per_minute} prompts this minute. {remaining} remaining.", 0)
            return True

        # Calculate wait time until next slot opens
        if rate_limit_queue:
            wait_time = 60 - (current_time - rate_limit_queue[0])
            if wait_time > 0:
                log(f"Rate limit reached. Next slot available in {wait_time:.1f} seconds.", 1)
            return False

    return True

def track_prompt(prompt_text):
    """Track a sent prompt and update statistics"""
    global prompts_sent_today, prompts_sent_total, last_prompt_time, prompt_history

    current_time = time.time()
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")

    # Update counters
    prompts_sent_today += 1
    prompts_sent_total += 1
    last_prompt_time = current_time

    # Truncate prompt text for display
    short_prompt = prompt_text[:50] + "..." if len(prompt_text) > 50 else prompt_text

    # Add to history (keep last 100 prompts)
    prompt_history.append({"time": timestamp, "prompt": short_prompt})
    if len(prompt_history) > 100:
        prompt_history = prompt_history[-100:]

    # Save to CSV file (append mode)
    try:
        with open(prompt_tracking_file, "a", encoding="utf-8") as f:
            if os.path.getsize(prompt_tracking_file) == 0:
                f.write("timestamp,prompt\n")
            escaped_prompt = prompt_text.replace('"', '""')
            f.write(f'"{timestamp}","{escaped_prompt}"\n')
    except Exception as e:
        log(f"Failed to save prompt history: {e}", 2)

    # Update the UI
    update_prompt_stats()

def initialize_browser():
    """Initialize DrissionPage browser with stealth settings"""
    global page
    try:
        # Configure browser options for stealth and performance
        options = ChromiumOptions()

        # Set custom profile path for MidJourney
        user_data_dir = os.path.join(os.path.expanduser("~"), "chrome_profiles", "midjourney_profile")
        os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')

        # Stealth settings
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--disable-features=VizDisplayCompositor')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-renderer-backgrounding')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--memory-pressure-off')
        options.set_argument('--max_old_space_size=1024')

        # Anti-detection
        options.set_argument('--disable-blink-features=AutomationControlled')
        options.set_argument('--disable-features=VizDisplayCompositor')
        options.set_argument('--no-first-run')
        options.set_argument('--no-default-browser-check')

        # Set realistic user agent
        options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # Headless mode (set to False if you want to see browser)
        options.headless(False)  # Changed to False for debugging

        # Initialize page
        page = ChromiumPage(addr_or_opts=options)

        # Additional stealth measures
        page.run_js('''
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Mock chrome runtime
            window.chrome = {
                runtime: {},
            };

            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });

            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        ''')

        log("DrissionPage browser initialized successfully with custom profile for MidJourney")
        return True

    except Exception as e:
        log(f"Failed to initialize browser: {e}", 3)
        return False

def navigate_to_midjourney():
    """Navigate to Midjourney with human-like behavior"""
    global page
    try:
        # Navigate to Midjourney
        log("Navigating to Midjourney...")
        page.get('https://www.midjourney.com/imagine')

        # Wait for page to load
        log("Waiting for page to load...")
        time.sleep(random.uniform(3, 5))

        # Print current URL and title for debugging
        log(f"Current URL: {page.url}")
        log(f"Page title: {page.title}")

        # Check if textarea is present
        log("Looking for prompt input field...")
        textarea = page.ele('tag:textarea', timeout=10)
        if textarea:
            log("Successfully navigated to Midjourney")
            return True
        else:
            log("Could not find prompt input field")
            # Try to find any input fields for debugging
            inputs = page.eles('tag:input')
            log(f"Found {len(inputs)} input elements")
            textareas = page.eles('tag:textarea')
            log(f"Found {len(textareas)} textarea elements")

            # Check if we're on a login page or blocked
            if "login" in page.url.lower() or "sign" in page.url.lower():
                log("Appears to be redirected to login page")
            elif "cloudflare" in page.html.lower():
                log("Cloudflare protection detected")

            return False

    except Exception as e:
        log(f"Failed to navigate: {e}", 3)
        return False

def submit_prompt(prompt_text, use_aspect_ratio=True):
    """Submit a prompt to Midjourney"""
    global page
    try:
        # Add aspect ratio if enabled
        if use_aspect_ratio:
            ar = random.choice(aspect_ratios)
            prompt_text = f"{prompt_text} --ar {ar}"

        # Find textarea with multiple selectors
        textarea_selectors = [
            'tag:textarea',
            '@data-testid=prompt-input',
            'tag:input@@type=text',
            '.prompt-input'
        ]

        textarea = None
        for selector in textarea_selectors:
            try:
                textarea = page.ele(selector, timeout=3)
                if textarea:
                    break
            except:
                continue

        if not textarea:
            log("Could not find prompt input field", 2)
            return False

        # Human-like interaction
        # Move mouse to textarea
        textarea.hover()
        time.sleep(random.uniform(0.2, 0.5))

        # Click and clear
        textarea.click()
        time.sleep(random.uniform(0.1, 0.3))

        # Clear existing text
        textarea.clear()
        time.sleep(random.uniform(0.1, 0.2))

        # Use copy-paste for faster input
        textarea.input(prompt_text)

        time.sleep(random.uniform(0.5, 1.0))

        # Submit
        textarea.input('\n')  # Press Enter
        time.sleep(random.uniform(1, 2))

        log(f"Successfully submitted: {prompt_text[:50]}...")
        return True

    except Exception as e:
        log(f"Failed to submit prompt: {e}", 3)
        return False

def load_csv():
    global csv_iterator, current_chunk, current_row, rows_to_process, loaded_csv_path, total_rows

    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        log("No CSV loaded")
        return

    try:
        encoding = selected_encoding.get()
        try:
            csv_iterator, total_rows = CSVHandler.get_chunk_iterator(file_path, encoding, chunk_size)
            current_chunk = next(csv_iterator)
            log(f"Loaded CSV with {encoding} encoding - Total rows: {total_rows}")
        except UnicodeDecodeError:
            # Try alternative encodings
            for alt_encoding in ['latin1', 'ISO-8859-1', 'cp1252']:
                if alt_encoding == encoding:
                    continue
                try:
                    csv_iterator, total_rows = CSVHandler.get_chunk_iterator(file_path, alt_encoding, chunk_size)
                    current_chunk = next(csv_iterator)
                    selected_encoding.set(alt_encoding)
                    log(f"Loaded CSV with {alt_encoding} encoding - Total rows: {total_rows}")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError("Could not decode file with any common encoding")

        current_row = 1
        rows_to_process = total_rows
        row_count_entry.delete(0, tk.END)
        row_count_entry.insert(0, str(total_rows))
        loaded_csv_path = file_path

        # Enable buttons
        submit_button.config(state=tk.NORMAL)
        start_button.config(state=tk.NORMAL)
        row_count_entry.config(state=tk.NORMAL)

        gc.collect()

    except Exception as e:
        messagebox.showerror("Error", f"Failed to load CSV file: {e}")
        log(f"Failed to load CSV: {e}")
        start_button.config(state=tk.DISABLED)
        row_count_entry.config(state=tk.DISABLED)

def get_next_row_data():
    global current_chunk, csv_iterator, current_row

    try:
        if current_chunk is None or (current_row-1) >= len(current_chunk):
            try:
                if current_chunk is not None:
                    del current_chunk
                    gc.collect()

                current_chunk = next(csv_iterator)
                current_row = 1

                while len(current_chunk) == 0 and csv_iterator is not None:
                    try:
                        del current_chunk
                        current_chunk = next(csv_iterator)
                        gc.collect()
                    except StopIteration:
                        return None
            except StopIteration:
                return None

        row_data = current_chunk.iloc[current_row-1]['input_data']
        current_row += 1

        return str(row_data).strip() if pd.notna(row_data) else None

    except Exception as e:
        log(f"Error getting next row: {e}")
        return None

def open_web_page():
    global page
    try:
        if initialize_browser():
            if navigate_to_midjourney():
                log("Page loaded, ready to submit!")
                open_page_button.config(state=tk.DISABLED)
            else:
                log("Failed to navigate to Midjourney", 3)
        else:
            messagebox.showerror("Error", "Failed to initialize browser")
    except Exception as e:
        log(f"Failed to open web page: {e}", 3)
        messagebox.showerror("Error", f"Failed to open web page: {e}")

def submit_next_row():
    global current_row, browser_last_used

    if csv_iterator is None or current_chunk is None:
        log("Please load a CSV file first")
        return False

    browser_last_used = time.time()

    if not check_rate_limit():
        log("Rate limit reached. Please wait.", 1)
        return False

    try:
        input_value = get_next_row_data()
        if input_value is None:
            log(f"No more data at row {current_row}")
            return False

        # Optimized text processing
        input_value = str(input_value).strip().replace('\n', ' ').replace('\r', ' ')
        if not input_value:
            log(f"Empty data at row {current_row}")
            return False

        # Apply prefix and suffix with proper ordering
        final_prompt = input_value
        
        # Add prefix at the beginning
        prefix_text = prompt_prefix.get().strip()
        if prefix_text:
            final_prompt = f"{prefix_text} {final_prompt}"
        
        # Add suffix before aspect ratio
        suffix_text = prompt_suffix.get().strip()
        if suffix_text:
            final_prompt = f"{final_prompt} {suffix_text}"
        
        # Add aspect ratio at the end
        if use_ar_feature.get():
            smart_aspect_ratio = random.choice(aspect_ratios)
            final_prompt = f"{final_prompt} --ar {smart_aspect_ratio}"

        # Submit with optimized error handling
        success = submit_prompt(final_prompt, False)  # AR already added

        if success:
            track_prompt(final_prompt)

            # Async CSV processing for better performance
            if delete_after_sent.get():
                thread_pool.submit(
                    CSVHandler.save_progress,
                    loaded_csv_path,
                    [current_chunk.index[current_row - 2]],
                    selected_encoding.get()
                )

            log(f"Submitted row {current_row}", 1)
            return True
        else:
            log(f"Failed to submit row {current_row}", 3)
            return False

    except Exception as e:
        log(f"Submission failed at row {current_row}: {str(e)}", 3)
        return False

def update_prompt_stats():
    """Update the prompt statistics display"""
    if not hasattr(update_prompt_stats, "last_update") or time.time() - update_prompt_stats.last_update > 1.0:
        try:
            prompts_today_value.config(text=str(prompts_sent_today))
            prompts_total_value.config(text=str(prompts_sent_total))

            if last_prompt_time:
                time_diff = time.time() - last_prompt_time
                if time_diff < 60:
                    time_text = f"{int(time_diff)} seconds ago"
                elif time_diff < 3600:
                    time_text = f"{int(time_diff/60)} minutes ago"
                else:
                    time_text = f"{int(time_diff/3600)} hours ago"
                last_prompt_time_value.config(text=time_text)

            current_rate = len(rate_limit_queue)
            rate_display.config(text=f"{current_rate}/{rate_limit_per_minute} per minute")

            if total_rows > 0 and current_row > 0:
                progress_percent = min(100, (current_row / total_rows) * 100)
                progress_bar["value"] = progress_percent
                progress_text.config(text=f"{progress_percent:.1f}% ({current_row}/{total_rows})")

            update_prompt_stats.last_update = time.time()
        except Exception as e:
            log(f"Error updating prompt stats: {e}", 0)

def start_automation():
    global automation_active, rows_to_process, current_row

    try:
        rows_to_process = int(row_count_entry.get())
        if not isinstance(rows_to_process, int) or rows_to_process <= 0:
            messagebox.showerror("Error", "Please enter a valid positive integer for row count.")
            return
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for row count.")
        return

    if csv_iterator is None:
        log("Please load a CSV file first")
        return

    if not page:
        if not initialize_browser():
            return
        if not navigate_to_midjourney():
            return

    automation_active = True
    start_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    log("Automation started...")
    status_log.delete(1.0, tk.END)
    current_row = 1

    automation_thread = threading.Thread(target=automate_submission)
    automation_thread.daemon = True
    automation_thread.start()

def automate_submission():
    global current_row, automation_timeout, browser_last_used

    if not (automation_active and not paused and current_row < rows_to_process):
        return

    try:
        browser_last_used = time.time()

        batch_size = min(2, rate_limit_per_minute)

        for _ in range(batch_size):
            if not (automation_active and not paused and current_row < rows_to_process):
                break

            if not check_rate_limit():
                break

            submit_next_row()

            if automation_timeout > 0:
                time.sleep(min(automation_timeout / 1000, 2))

        gc.collect()

        if current_row < rows_to_process and automation_active and not paused:
            wait_time = max(5, 60 / rate_limit_per_minute)
            root.after(int(wait_time * 1000), automate_submission)
        else:
            stop_automation()
            log("Automation completed", 1)

    except Exception as e:
        log(f"Automation error: {str(e)}", 3)
        pause_automation()

def pause_automation():
    global paused
    paused = True
    log("Automation paused.")
    pause_button.config(state=tk.DISABLED)
    resume_button.config(state=tk.NORMAL)

def resume_automation():
    global paused
    paused = False
    log("Resuming automation...")
    resume_button.config(state=tk.DISABLED)
    pause_button.config(state=tk.NORMAL)
    automate_submission()

def stop_automation():
    global automation_active
    automation_active = False
    start_button.config(state=tk.NORMAL)
    pause_button.config(state=tk.DISABLED)
    log("Automation stopped.")

def close_app():
    global page, thread_pool
    try:
        # Stop automation
        global automation_active
        automation_active = False
        
        # Close browser
        if page:
            try:
                page.quit()
            except Exception as e:
                log(f"Error closing browser: {e}")
        
        # Shutdown thread pool
        thread_pool.shutdown(wait=False)
        
        # Force garbage collection
        gc.collect()
        
    except Exception as e:
        log(f"Error during cleanup: {e}")
    finally:
        root.destroy()

def clear_log():
    status_log.delete(1.0, tk.END)

def update_timeout():
    global automation_timeout
    try:
        automation_timeout = int(timeout_entry.get())
        log(f"Timeout updated to: {automation_timeout} ms")
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for timeout.")

def update_rate_limit():
    global rate_limit_per_minute
    try:
        new_limit = int(rate_limit_entry.get())
        if new_limit > 0:
            rate_limit_per_minute = new_limit
            log(f"Rate limit updated to: {rate_limit_per_minute} prompts per minute")
        else:
            messagebox.showerror("Error", "Please enter a positive integer for rate limit.")
    except ValueError:
        messagebox.showerror("Error", "Please enter a valid integer for rate limit.")

# --- UI Layout using Grid ---
# Main container Frame
main_frame = tk.Frame(root, padx=10, pady=10)
main_frame.pack(fill=tk.BOTH, expand=True)

# Control Frame
control_frame = tk.LabelFrame(main_frame, text="Controllers", padx=5, pady=5)
control_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

load_button = tk.Button(control_frame, text="Load CSV", command=load_csv)
load_button.grid(row=0, column=0, pady=5, padx=5, sticky="ew")

open_page_button = tk.Button(control_frame, text="Open Web Page", command=open_web_page)
open_page_button.grid(row=0, column=1, pady=5, padx=5, sticky="ew")

submit_button = tk.Button(control_frame, text="Submit Next Row", command=submit_next_row, state=tk.DISABLED)
submit_button.grid(row=1, column=0, pady=5, padx=5, sticky="ew")

start_button = tk.Button(control_frame, text="Start Automation", command=start_automation, state=tk.DISABLED)
start_button.grid(row=1, column=1, pady=5, padx=5, sticky="ew")

pause_button = tk.Button(control_frame, text="Pause Automation", command=pause_automation, state=tk.DISABLED)
pause_button.grid(row=2, column=0, pady=5, padx=5, sticky="ew")

resume_button = tk.Button(control_frame, text="Resume Automation", command=resume_automation, state=tk.DISABLED)
resume_button.grid(row=2, column=1, pady=5, padx=5, sticky="ew")

stop_button = tk.Button(control_frame, text="Stop Automation", command=stop_automation)
stop_button.grid(row=3, column=0, pady=5, padx=5, sticky="ew")

clear_log_button = tk.Button(control_frame, text="Clear Log", command=clear_log)
clear_log_button.grid(row=3, column=1, pady=5, padx=5, sticky="ew")

# Settings Frame
settings_frame = tk.LabelFrame(main_frame, text="Settings", padx=5, pady=5)
settings_frame.grid(row=0, column=1, sticky="ew", padx=10, pady=10)

# Row Count Input
row_count_label = tk.Label(settings_frame, text="Rows to Process:")
row_count_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")

row_count_entry = tk.Entry(settings_frame, state=tk.DISABLED)
row_count_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

# Encoding selection
encoding_label = tk.Label(settings_frame, text="CSV Encoding:")
encoding_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")

encoding_options = ['utf-8', 'latin1', 'ISO-8859-1', 'cp1252']
encoding_dropdown = ttk.Combobox(settings_frame, textvariable=selected_encoding, values=encoding_options, state="readonly")
encoding_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

# Timeout Input
timeout_label = tk.Label(settings_frame, text="Timeout (ms):")
timeout_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")

timeout_entry = tk.Entry(settings_frame)
timeout_entry.insert(0, str(automation_timeout))
timeout_entry.grid(row=2, column=1, padx=5, pady=5, sticky="ew")

update_timeout_button = tk.Button(settings_frame, text="Update Timeout", command=update_timeout)
update_timeout_button.grid(row=2, column=2, padx=5, pady=5, sticky="ew")

# Rate limit settings
rate_limit_label = tk.Label(settings_frame, text="Prompts Per Minute:")
rate_limit_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")

rate_limit_entry = tk.Entry(settings_frame)
rate_limit_entry.insert(0, str(rate_limit_per_minute))
rate_limit_entry.grid(row=3, column=1, padx=5, pady=5, sticky="ew")

update_rate_button = tk.Button(settings_frame, text="Update Rate", command=update_rate_limit)
update_rate_button.grid(row=3, column=2, padx=5, pady=5, sticky="ew")

# Delete after sent checkbox
delete_checkbox = tk.Checkbutton(settings_frame, text="Delete Row After Sent", variable=delete_after_sent)
delete_checkbox.grid(row=4, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# AR feature checkbox
ar_checkbox = tk.Checkbutton(settings_frame, text="Use Aspect Ratio (--ar) Feature", variable=use_ar_feature)
ar_checkbox.grid(row=5, column=0, columnspan=2, pady=5, padx=5, sticky="w")

# Prompt prefix input
prefix_label = tk.Label(settings_frame, text="Prompt Prefix:")
prefix_label.grid(row=6, column=0, padx=5, pady=5, sticky="w")

prefix_entry = tk.Entry(settings_frame, textvariable=prompt_prefix)
prefix_entry.grid(row=6, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

# Prompt suffix input
suffix_label = tk.Label(settings_frame, text="Prompt Suffix:")
suffix_label.grid(row=7, column=0, padx=5, pady=5, sticky="w")

suffix_entry = tk.Entry(settings_frame, textvariable=prompt_suffix)
suffix_entry.grid(row=7, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

# Log level selector
log_level_label = tk.Label(settings_frame, text="Log Level:")
log_level_label.grid(row=8, column=0, padx=5, pady=5, sticky="w")

log_levels = ["Debug", "Info", "Warning", "Error"]
log_level_dropdown = ttk.Combobox(settings_frame, textvariable=log_level_var, values=log_levels, state="readonly")
log_level_dropdown.grid(row=8, column=1, padx=5, pady=5, sticky="ew")
log_level_dropdown.bind("<<ComboboxSelected>>", lambda e: set_log_level())

# Statistics Frame
stats_frame = tk.LabelFrame(main_frame, text="Statistics", padx=5, pady=5)
stats_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=10, pady=5)

# Prompts sent today
prompts_today_label = tk.Label(stats_frame, text="Prompts Today:")
prompts_today_label.grid(row=0, column=0, padx=5, pady=2, sticky="w")
prompts_today_value = tk.Label(stats_frame, text="0", font=("Arial", 10, "bold"))
prompts_today_value.grid(row=0, column=1, padx=5, pady=2, sticky="w")

# Total prompts sent
prompts_total_label = tk.Label(stats_frame, text="Total Prompts:")
prompts_total_label.grid(row=0, column=2, padx=5, pady=2, sticky="w")
prompts_total_value = tk.Label(stats_frame, text="0", font=("Arial", 10, "bold"))
prompts_total_value.grid(row=0, column=3, padx=5, pady=2, sticky="w")

# Last prompt time
last_prompt_time_label = tk.Label(stats_frame, text="Last Sent:")
last_prompt_time_label.grid(row=1, column=0, padx=5, pady=2, sticky="w")
last_prompt_time_value = tk.Label(stats_frame, text="Never", font=("Arial", 9))
last_prompt_time_value.grid(row=1, column=1, padx=5, pady=2, sticky="w")

# Rate display
rate_label = tk.Label(stats_frame, text="Current Rate:")
rate_label.grid(row=1, column=2, padx=5, pady=2, sticky="w")
rate_display = tk.Label(stats_frame, text="0/3 per minute", font=("Arial", 9))
rate_display.grid(row=1, column=3, padx=5, pady=2, sticky="w")

# Progress bar
progress_label = tk.Label(stats_frame, text="Progress:")
progress_label.grid(row=2, column=0, padx=5, pady=2, sticky="w")
progress_bar = ttk.Progressbar(stats_frame, length=200, mode='determinate')
progress_bar.grid(row=2, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
progress_text = tk.Label(stats_frame, text="0.0% (0/0)", font=("Arial", 9))
progress_text.grid(row=2, column=3, padx=5, pady=2, sticky="w")

# Status Log Frame
log_frame = tk.LabelFrame(main_frame, text="Status Log", padx=5, pady=5)
log_frame.grid(row=2, column=0, columnspan=2, sticky="nsew", padx=10, pady=5)

status_log = scrolledtext.ScrolledText(log_frame, height=10, width=80)
status_log.pack(fill=tk.BOTH, expand=True)

# Configure grid weights for resizing
main_frame.grid_columnconfigure(0, weight=1)
main_frame.grid_columnconfigure(1, weight=1)
main_frame.grid_rowconfigure(2, weight=1)
stats_frame.grid_columnconfigure(1, weight=1)
stats_frame.grid_columnconfigure(2, weight=1)

# Bind close event
root.protocol("WM_DELETE_WINDOW", close_app)

import queue
from concurrent.futures import ThreadPoolExecutor

# Initialize thread pool and UI update queue
thread_pool = ThreadPoolExecutor(max_workers=2)
ui_update_queue = queue.Queue()

def process_ui_updates():
    """Process queued UI updates for better performance"""
    try:
        while not ui_update_queue.empty():
            update_func = ui_update_queue.get_nowait()
            update_func()
    except queue.Empty:
        pass
    except Exception as e:
        log(f"Error processing UI updates: {e}", 0)
    finally:
        # Schedule next update processing
        root.after(100, process_ui_updates)

# Start the GUI with performance optimizations
if __name__ == "__main__":
    log("DrissionPage CSV Submitter Tool started")
    log("Load a CSV file and open the web page to begin")
    
    # Start UI update processing
    root.after(100, process_ui_updates)
    
    # Optimize Tkinter performance
    root.tk.call('tk', 'scaling', 1.0)  # Disable DPI scaling for performance
    
    try:
        root.mainloop()
    finally:
        # Cleanup on exit
        if page:
            try:
                page.quit()
            except:
                pass
        thread_pool.shutdown(wait=False)
